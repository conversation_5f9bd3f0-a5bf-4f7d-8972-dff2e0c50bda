/*
 * COMPREHENSIVE TEXT VISIBILITY SYSTEM
 * White Wings Visa Website - Enhanced Text Visibility
 * Ensures perfect readability while maintaining beautiful design
 */

/* ========================================
   CORE TEXT VISIBILITY RULES
======================================== */

/* Base text colors for maximum readability */
body, html {
    color: #1a1a1a !important;
    font-family: 'Gilroy', -apple-system, BlinkMacSystemFont, sans-serif !important;
}

/* All standard text elements - Dark and readable */
p, span, div, li, ul, ol, td, th, label, strong, b, em, small,
.text-content, .content, .description, .info, .details {
    color: #1a1a1a !important;
    font-weight: 400 !important;
    line-height: 1.6 !important;
}

/* All headings - Extra dark with proper hierarchy */
h1, h2, h3, h4, h5, h6 {
    color: #000000 !important;
    font-weight: 600 !important;
    line-height: 1.3 !important;
    margin-bottom: 1rem !important;
}

/* Navigation elements - Professional dark */
nav, .navbar, .nav-div, .navigation, .menu {
    color: #1a1a1a !important;
}

nav a, .navbar a, .nav-link, .menu-item, .dropdown-item, .nav-item {
    color: #1a1a1a !important;
    font-weight: 500 !important;
    text-decoration: none !important;
}

nav a:hover, .navbar a:hover, .nav-link:hover, .menu-item:hover {
    color: #0369A1 !important;
    transition: color 0.3s ease !important;
}

/* ========================================
   HERO SECTIONS & TEXT ON IMAGES
======================================== */

/* Hero sections - All pages */
.hero, .hero-section, .hero-content, .hero-text,
.contact-hero-section, .about-hero-section, .study-hero-section,
.work-hero-section, .migrate-hero-section, .visit-hero-section,
.banner, .slider, .swiper-slide, .cta-section, .apply-visa-cta,
[style*="background-image"], .bg-image, .bg-overlay, .image-section {
    position: relative !important;
}

/* Text on dark backgrounds - WHITE with shadow for perfect visibility */
.hero h1, .hero h2, .hero h3, .hero h4, .hero h5, .hero h6, .hero p, .hero span,
.hero-section h1, .hero-section h2, .hero-section h3, .hero-section h4, .hero-section h5, .hero-section h6, .hero-section p, .hero-section span,
.contact-hero-section h1, .contact-hero-section h2, .contact-hero-section h3, .contact-hero-section h4, .contact-hero-section h5, .contact-hero-section h6, .contact-hero-section p, .contact-hero-section span,
.about-hero-section h1, .about-hero-section h2, .about-hero-section h3, .about-hero-section h4, .about-hero-section h5, .about-hero-section h6, .about-hero-section p, .about-hero-section span,
.study-hero-section h1, .study-hero-section h2, .study-hero-section h3, .study-hero-section h4, .study-hero-section h5, .study-hero-section h6, .study-hero-section p, .study-hero-section span,
.work-hero-section h1, .work-hero-section h2, .work-hero-section h3, .work-hero-section h4, .work-hero-section h5, .work-hero-section h6, .work-hero-section p, .work-hero-section span,
.migrate-hero-section h1, .migrate-hero-section h2, .migrate-hero-section h3, .migrate-hero-section h4, .migrate-hero-section h5, .migrate-hero-section h6, .migrate-hero-section p, .migrate-hero-section span,
.visit-hero-section h1, .visit-hero-section h2, .visit-hero-section h3, .visit-hero-section h4, .visit-hero-section h5, .visit-hero-section h6, .visit-hero-section p, .visit-hero-section span,
.banner h1, .banner h2, .banner h3, .banner h4, .banner h5, .banner h6, .banner p, .banner span,
.slider h1, .slider h2, .slider h3, .slider h4, .slider h5, .slider h6, .slider p, .slider span,
.swiper-slide h1, .swiper-slide h2, .swiper-slide h3, .swiper-slide h4, .swiper-slide h5, .swiper-slide h6, .swiper-slide p, .swiper-slide span,
[style*="background-image"] h1, [style*="background-image"] h2, [style*="background-image"] h3, [style*="background-image"] h4, [style*="background-image"] h5, [style*="background-image"] h6, [style*="background-image"] p, [style*="background-image"] span,
.bg-image h1, .bg-image h2, .bg-image h3, .bg-image h4, .bg-image h5, .bg-image h6, .bg-image p, .bg-image span,
.bg-overlay h1, .bg-overlay h2, .bg-overlay h3, .bg-overlay h4, .bg-overlay h5, .bg-overlay h6, .bg-overlay p, .bg-overlay span,
.image-section h1, .image-section h2, .image-section h3, .image-section h4, .image-section h5, .image-section h6, .image-section p, .image-section span,
.cta-section h1, .cta-section h2, .cta-section h3, .cta-section h4, .cta-section h5, .cta-section h6, .cta-section p, .cta-section span,
.apply-visa-cta h1, .apply-visa-cta h2, .apply-visa-cta h3, .apply-visa-cta h4, .apply-visa-cta h5, .apply-visa-cta h6, .apply-visa-cta p, .apply-visa-cta span,
.testimonials h1, .testimonials h2, .testimonials h3, .testimonials h4, .testimonials h5, .testimonials h6, .testimonials p, .testimonials span {
    color: #FFFFFF !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.7) !important;
    font-weight: 600 !important;
    letter-spacing: 0.02em !important;
    z-index: 5 !important;
    position: relative !important;
}

/* ========================================
   CARD ELEMENTS
======================================== */

/* Card elements - Clean, professional dark text */
.card, .service-card, .feature-card, .benefit-card, .destination-card,
.testimonial-card, .contact-card, .about-card, .study-card,
.work-card, .migrate-card, .visit-card, .country-universities, .university-logo {
    background-color: #FFFFFF !important;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
    border-radius: 16px !important;
    overflow: hidden !important;
    transition: all 0.3s ease !important;
}

/* Card headings */
.card h1, .card h2, .card h3, .card h4, .card h5, .card h6,
.service-card h1, .service-card h2, .service-card h3, .service-card h4, .service-card h5, .service-card h6,
.feature-card h1, .feature-card h2, .feature-card h3, .feature-card h4, .feature-card h5, .feature-card h6,
.benefit-card h1, .benefit-card h2, .benefit-card h3, .benefit-card h4, .benefit-card h5, .benefit-card h6,
.destination-card h1, .destination-card h2, .destination-card h3, .destination-card h4, .destination-card h5, .destination-card h6,
.testimonial-card h1, .testimonial-card h2, .testimonial-card h3, .testimonial-card h4, .testimonial-card h5, .testimonial-card h6,
.contact-card h1, .contact-card h2, .contact-card h3, .contact-card h4, .contact-card h5, .contact-card h6,
.about-card h1, .about-card h2, .about-card h3, .about-card h4, .about-card h5, .about-card h6,
.study-card h1, .study-card h2, .study-card h3, .study-card h4, .study-card h5, .study-card h6,
.work-card h1, .work-card h2, .work-card h3, .work-card h4, .work-card h5, .work-card h6,
.migrate-card h1, .migrate-card h2, .migrate-card h3, .migrate-card h4, .migrate-card h5, .migrate-card h6,
.visit-card h1, .visit-card h2, .visit-card h3, .visit-card h4, .visit-card h5, .visit-card h6 {
    color: #000000 !important;
    font-weight: 600 !important;
    margin-bottom: 0.75rem !important;
}

/* Card text content */
.card p, .card span, .card div, .card li,
.service-card p, .service-card span, .service-card div, .service-card li,
.feature-card p, .feature-card span, .feature-card div, .feature-card li,
.benefit-card p, .benefit-card span, .benefit-card div, .benefit-card li,
.destination-card p, .destination-card span, .destination-card div, .destination-card li,
.testimonial-card p, .testimonial-card span, .testimonial-card div, .testimonial-card li,
.contact-card p, .contact-card span, .contact-card div, .contact-card li,
.about-card p, .about-card span, .about-card div, .about-card li,
.study-card p, .study-card span, .study-card div, .study-card li,
.work-card p, .work-card span, .work-card div, .work-card li,
.migrate-card p, .migrate-card span, .migrate-card div, .migrate-card li,
.visit-card p, .visit-card span, .visit-card div, .visit-card li {
    color: #1a1a1a !important;
    line-height: 1.6 !important;
    font-weight: 400 !important;
}

/* ========================================
   SECTION CONTENT
======================================== */

/* Section content - Clean, readable text */
.section, .container, .content, .main-content, .page-content,
.services-section, .about-section, .process-section, .faq-section,
.stats-section, .features-section, .benefits-section,
.destinations-section, .timeline, .assessment-section, .university-placements,
.contact-form, .thank-you-section, .error-section {
    color: #1a1a1a !important;
    line-height: 1.6 !important;
}

/* Section headings */
.section h1, .section h2, .section h3, .section h4, .section h5, .section h6,
.container h1, .container h2, .container h3, .container h4, .container h5, .container h6,
.content h1, .content h2, .content h3, .content h4, .content h5, .content h6,
.main-content h1, .main-content h2, .main-content h3, .main-content h4, .main-content h5, .main-content h6,
.page-content h1, .page-content h2, .page-content h3, .page-content h4, .page-content h5, .page-content h6 {
    color: #000000 !important;
    font-weight: 600 !important;
    margin-bottom: 1rem !important;
}

/* ========================================
   BUTTON ELEMENTS
======================================== */

/* Button text - WHITE on blue background for maximum contrast */
.btn, button, .btn-primary, .btn-secondary, .btn-apply-now,
.submit-btn, .assessment-submit, .apply-btn, input[type="submit"],
.ww-btn-primary, .ww-btn-secondary, .ww-btn-submit, .cta-button,
.action-button, .hero-button, .nav-button, .form-button {
    background-color: #0369A1 !important;
    color: #FFFFFF !important;
    font-weight: 600 !important;
    border: none !important;
    padding: 12px 24px !important;
    border-radius: 8px !important;
    transition: all 0.3s ease !important;
    text-decoration: none !important;
    display: inline-block !important;
}

.btn:hover, button:hover, .btn-primary:hover, .btn-secondary:hover, .btn-apply-now:hover,
.submit-btn:hover, .assessment-submit:hover, .apply-btn:hover, input[type="submit"]:hover {
    background-color: #0284c7 !important;
    color: #FFFFFF !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(3, 105, 161, 0.3) !important;
}

/* ========================================
   FORM ELEMENTS
======================================== */

/* Form elements - Dark text on light backgrounds */
input, textarea, select, .form-control, .form-group, .form-field {
    color: #1a1a1a !important;
    background-color: #FFFFFF !important;
    border: 2px solid #D1D5DB !important;
    border-radius: 8px !important;
    padding: 12px 16px !important;
    font-size: 16px !important;
    font-weight: 400 !important;
    transition: border-color 0.3s ease !important;
}

input:focus, textarea:focus, select:focus, .form-control:focus {
    border-color: #0369A1 !important;
    outline: none !important;
    box-shadow: 0 0 0 3px rgba(3, 105, 161, 0.1) !important;
}

/* Form labels */
label, .form-label, .form-group label {
    color: #1a1a1a !important;
    font-weight: 500 !important;
    margin-bottom: 8px !important;
    display: block !important;
}

/* Placeholder text */
input::placeholder, textarea::placeholder, select::placeholder {
    color: #6B7280 !important;
    opacity: 1 !important;
}

/* ========================================
   FOOTER ELEMENTS
======================================== */

/* Footer elements - Professional dark text */
footer, .footer-section, .main-footer, .footer-container {
    background-color: #F8FAFC !important;
    color: #1a1a1a !important;
    padding: 40px 0 !important;
    border-top: 1px solid #E5E7EB !important;
}

footer h1, footer h2, footer h3, footer h4, footer h5, footer h6,
.footer-section h1, .footer-section h2, .footer-section h3, .footer-section h4, .footer-section h5, .footer-section h6,
.main-footer h1, .main-footer h2, .main-footer h3, .main-footer h4, .main-footer h5, .main-footer h6,
.footer-container h1, .footer-container h2, .footer-container h3, .footer-container h4, .footer-container h5, .footer-container h6 {
    color: #000000 !important;
    font-weight: 600 !important;
    margin-bottom: 1rem !important;
}

footer p, footer span, footer div, footer li, footer a,
.footer-section p, .footer-section span, .footer-section div, .footer-section li, .footer-section a,
.main-footer p, .main-footer span, .main-footer div, .main-footer li, .main-footer a,
.footer-container p, .footer-container span, .footer-container div, .footer-container li, .footer-container a {
    color: #1a1a1a !important;
    font-weight: 400 !important;
    line-height: 1.6 !important;
    text-decoration: none !important;
}

footer a:hover, .footer-section a:hover, .main-footer a:hover, .footer-container a:hover {
    color: #0369A1 !important;
    transition: color 0.3s ease !important;
}

/* ========================================
   MOBILE MENU & NAVIGATION
======================================== */

/* Mobile menu - Clean white background with dark text */
.mobile-menu, .mobile-nav-links, .mobile-services-dropdown, .mobile-menu-overlay {
    background-color: #FFFFFF !important;
    color: #1a1a1a !important;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1) !important;
    border-radius: 0 0 16px 16px !important;
    z-index: 1000 !important;
}

.mobile-menu a, .mobile-nav-links a, .mobile-services-dropdown a,
.mobile-menu-item, .mobile-nav-item {
    color: #1a1a1a !important;
    font-weight: 500 !important;
    padding: 12px 20px !important;
    text-decoration: none !important;
    border-bottom: 1px solid #F3F4F6 !important;
    transition: all 0.3s ease !important;
}

.mobile-menu a:hover, .mobile-nav-links a:hover, .mobile-services-dropdown a:hover,
.mobile-menu-item:hover, .mobile-nav-item:hover {
    background-color: #F8FAFC !important;
    color: #0369A1 !important;
    padding-left: 24px !important;
}

/* ========================================
   DROPDOWN MENUS
======================================== */

/* Dropdown menus - Professional styling */
.dropdown-content, .dropdown-menu, .services-dropdown {
    background-color: #FFFFFF !important;
    color: #1a1a1a !important;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1) !important;
    border-radius: 8px !important;
    border: 1px solid #E5E7EB !important;
    z-index: 1000 !important;
    overflow: hidden !important;
}

.dropdown-content a, .dropdown-menu a, .services-dropdown a,
.dropdown-item, .dropdown-link {
    color: #1a1a1a !important;
    font-weight: 400 !important;
    padding: 12px 16px !important;
    text-decoration: none !important;
    border-bottom: 1px solid #F3F4F6 !important;
    transition: all 0.3s ease !important;
    display: block !important;
}

.dropdown-content a:hover, .dropdown-menu a:hover, .services-dropdown a:hover,
.dropdown-item:hover, .dropdown-link:hover {
    background-color: #F8FAFC !important;
    color: #0369A1 !important;
}

/* ========================================
   SLIDER & CAROUSEL CONTENT
======================================== */

/* Swiper/Slider content - Special handling for image backgrounds */
.swiper-slide, .slider-content, .carousel-item, .testimonial-slider {
    position: relative !important;
}

/* Text on slider images - WHITE with strong shadow */
.swiper-slide h1, .swiper-slide h2, .swiper-slide h3, .swiper-slide h4, .swiper-slide h5, .swiper-slide h6,
.swiper-slide p, .swiper-slide span, .swiper-slide div,
.slider-content h1, .slider-content h2, .slider-content h3, .slider-content h4, .slider-content h5, .slider-content h6,
.slider-content p, .slider-content span, .slider-content div,
.carousel-item h1, .carousel-item h2, .carousel-item h3, .carousel-item h4, .carousel-item h5, .carousel-item h6,
.carousel-item p, .carousel-item span, .carousel-item div {
    color: #FFFFFF !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.7) !important;
    font-weight: 600 !important;
    z-index: 5 !important;
    position: relative !important;
}

/* Testimonial cards - Dark text on white background */
.testimonial-card h1, .testimonial-card h2, .testimonial-card h3, .testimonial-card h4, .testimonial-card h5, .testimonial-card h6,
.testimonial-card p, .testimonial-card span, .testimonial-card div {
    color: #1a1a1a !important;
    text-shadow: none !important;
    background-color: #FFFFFF !important;
    padding: 20px !important;
    border-radius: 12px !important;
}

/* ========================================
   LISTS & TABLES
======================================== */

/* Lists - Clean, readable formatting */
ul, ol, li {
    color: #1a1a1a !important;
    line-height: 1.6 !important;
    margin-bottom: 0.5rem !important;
}

ul li::marker, ol li::marker {
    color: #0369A1 !important;
}

/* Tables - Professional styling */
table, th, td, thead, tbody, tfoot {
    color: #1a1a1a !important;
    border-color: #E5E7EB !important;
}

th {
    background-color: #F8FAFC !important;
    color: #000000 !important;
    font-weight: 600 !important;
    padding: 12px 16px !important;
}

td {
    padding: 12px 16px !important;
    border-bottom: 1px solid #F3F4F6 !important;
}

/* ========================================
   BADGES, TAGS & LABELS
======================================== */

/* Badges and tags - Accent styling */
.badge, .tag, .section-badge, .label, .chip {
    background-color: #0369A1 !important;
    color: #FFFFFF !important;
    font-weight: 500 !important;
    padding: 4px 12px !important;
    border-radius: 20px !important;
    font-size: 14px !important;
    display: inline-block !important;
}

/* ========================================
   ICONS & SPECIAL ELEMENTS
======================================== */

/* Icons - Consistent coloring */
i[class*="ri-"], .icon, [class*="icon-"] {
    color: #0369A1 !important;
    font-size: 20px !important;
}

/* Card icons - Special blue background with white icon */
.card i, .service-card i, .feature-card i, .benefit-card i,
.testimonial-card i, .contact-card i, .about-card i, .study-card i,
.work-card i, .migrate-card i, .visit-card i {
    background-color: #0369A1 !important;
    color: #FFFFFF !important;
    width: 40px !important;
    height: 40px !important;
    border-radius: 50% !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
}

/* ========================================
   LINKS & EMPHASIS
======================================== */

/* Links - Professional styling */
a {
    color: #1a1a1a !important;
    text-decoration: none !important;
    transition: color 0.3s ease !important;
}

a:hover, a:focus, a:active {
    color: #0369A1 !important;
    text-decoration: underline !important;
}

/* Special emphasis */
strong, b, .font-weight-bold, .fw-bold, .bold {
    color: #000000 !important;
    font-weight: 700 !important;
}

em, i, .italic {
    color: #1a1a1a !important;
    font-style: italic !important;
}

/* ========================================
   COMPREHENSIVE OVERRIDES
======================================== */

/* Override any problematic text colors */
.text-white, .text-light, .text-muted, .text-secondary,
[style*="color: white"], [style*="color: #fff"], [style*="color: #ffffff"],
[style*="color: rgba(255,255,255"], [style*="color: rgb(255,255,255"] {
    color: #1a1a1a !important;
}

/* Exception: Keep white text on hero/image sections */
.hero .text-white, .hero-section .text-white, .swiper-slide .text-white,
.banner .text-white, .slider .text-white, [style*="background-image"] .text-white {
    color: #FFFFFF !important;
}

/* Ensure all content wrappers are readable */
.content-wrapper, .page-wrapper, .main-wrapper, .site-wrapper {
    color: #1a1a1a !important;
    line-height: 1.6 !important;
}

/* ========================================
   FINAL SAFETY OVERRIDES
======================================== */

/* Ensure text is never transparent or hidden */
* {
    opacity: 1 !important;
}

/* Remove problematic text shadows except for hero sections */
* {
    text-shadow: none !important;
}

/* Add text shadow only for text on images */
.hero *, .hero-section *, .swiper-slide *, .banner *, .slider *,
[style*="background-image"] *, .bg-image *, .bg-overlay *,
.image-section *, .cta-section *, .apply-visa-cta * {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.7) !important;
}

/* Final button override */
button, .btn, [class*="btn-"], input[type="submit"] {
    background-color: #0369A1 !important;
    color: #FFFFFF !important;
    font-weight: 600 !important;
}

/* Final heading override */
h1, h2, h3, h4, h5, h6 {
    color: #000000 !important;
    font-weight: 600 !important;
}
