// Simple Mobile Menu Script
document.addEventListener('DOMContentLoaded', function() {
    // Get elements
    const hamburger = document.getElementById('simple-hamburger');
    const mobileMenu = document.querySelector('.mobile-menu-sidebar');
    const mobileMenuOverlay = document.querySelector('.mobile-menu-overlay');
    const closeButton = document.querySelector('.mobile-menu-close');
    const servicesToggle = document.querySelector('.mobile-services-toggle');
    const servicesLinks = document.querySelector('.mobile-services-links');
    
    // Open mobile menu
    window.openMobileMenu = function() {
        mobileMenu.style.right = '0';
        mobileMenuOverlay.style.display = 'block';
        document.body.style.overflow = 'hidden';
    };
    
    // Close mobile menu
    window.closeMobileMenu = function() {
        mobileMenu.style.right = '-300px';
        mobileMenuOverlay.style.display = 'none';
        document.body.style.overflow = 'auto';
    };
    
    // Toggle services dropdown
    window.toggleServices = function() {
        const arrow = document.querySelector('.mobile-services-arrow');
        if (servicesLinks.style.maxHeight) {
            servicesLinks.style.maxHeight = null;
            arrow.style.transform = 'rotate(0deg)';
        } else {
            servicesLinks.style.maxHeight = servicesLinks.scrollHeight + 'px';
            arrow.style.transform = 'rotate(180deg)';
        }
    };
    
    // Close menu when clicking outside
    if (mobileMenuOverlay) {
        mobileMenuOverlay.addEventListener('click', closeMobileMenu);
    }
    
    // Close menu when clicking close button
    if (closeButton) {
        closeButton.addEventListener('click', closeMobileMenu);
    }
    
    // Open menu when clicking hamburger
    if (hamburger) {
        hamburger.addEventListener('click', openMobileMenu);
    }
});