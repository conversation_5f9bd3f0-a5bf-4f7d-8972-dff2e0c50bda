/* Enhanced CTA Section Styles */
.codex-cta {
    padding: 120px 0;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.7)), 
                url('https://images.unsplash.com/photo-1488646953014-85cb44e25828?ixlib=rb-4.0.3&auto=format&fit=crop&w=1500&q=80');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    color: white;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.codex-cta::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="%23ffffff" opacity="0.1"/></svg>') repeat;
    animation: float 15s ease-in-out infinite;
}

@keyframes float {
    0% { background-position: 0 0; }
    50% { background-position: 100px 50px; }
    100% { background-position: 0 0; }
}

/* Removed white shadow at bottom */

.codex-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 2;
}

.codex-cta-content {
    max-width: 800px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
    background: #a1aebb56;
    padding: 50px;
    border-radius: 20px;
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
    transform: perspective(1000px) rotateX(0deg);
    transition: all 0.5s ease;
}

.codex-cta-content:hover {
    transform: perspective(1000px) rotateX(2deg) translateY(-10px);
    box-shadow: 0 35px 60px rgba(0, 0, 0, 0.4);
}

.codex-cta-content h2 {
    font-size: 42px;
    margin-bottom: 25px;
    font-weight: 700;
    animation: fadeInUp 1s ease-out;
    position: relative;
    display: inline-block;
}

.codex-cta-content h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 3px;
    background: #ffffff;
    border-radius: 3px;
}

.codex-cta-content p {
    font-size: 18px;
    margin-bottom: 40px;
    opacity: 0.95;
    line-height: 1.6;
    animation: fadeInUp 1s ease-out 0.2s both;
}

.codex-btn-primary {
    display: inline-flex;
    align-items: center;
    gap: 12px;
    background: white;
    color: #000080;
    padding: 18px 40px;
    border-radius: 50px;
    font-weight: 700;
    font-size: 18px;
    text-decoration: none;
    transition: all 0.3s ease;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
    animation: fadeInUp 1s ease-out 0.4s both;
    position: relative;
    overflow: hidden;
    border: 2px solid transparent;
}

.codex-btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.1), transparent);
    transition: left 0.7s ease;
}

.codex-btn-primary:hover {
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
    color: #000080;
    border-color: #000080;
    background: rgba(255, 255, 255, 0.9);
}

.codex-btn-primary:hover::before {
    left: 100%;
}

.codex-btn-primary i {
    font-size: 20px;
    transition: transform 0.3s ease;
}

.codex-btn-primary:hover i {
    transform: translateX(5px);
}

/* Responsive styles */
@media (max-width: 768px) {
    .codex-cta {
        padding: 80px 0;
    }
    
    .codex-cta-content {
        padding: 30px;
    }
    
    .codex-cta-content h2 {
        font-size: 32px;
    }
    
    .codex-cta-content p {
        font-size: 16px;
        margin-bottom: 30px;
    }
    
    .codex-btn-primary {
        padding: 15px 30px;
        font-size: 16px;
    }
}

@media (max-width: 480px) {
    .codex-cta {
        padding: 60px 0;
    }
    
    .codex-cta-content {
        padding: 25px;
    }
    
    .codex-cta-content h2 {
        font-size: 28px;
    }
    
    .codex-cta-content p {
        font-size: 15px;
        margin-bottom: 25px;
    }
    
    .codex-btn-primary {
        padding: 12px 25px;
        font-size: 15px;
    }
}