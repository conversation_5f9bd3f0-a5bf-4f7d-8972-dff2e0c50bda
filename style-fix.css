/* Fix for Lenis scroll */
html.lenis {
  height: auto;
}

.lenis.lenis-smooth {
  scroll-behavior: auto;
}

.lenis.lenis-smooth [data-lenis-prevent] {
  overscroll-behavior: contain;
}

.lenis.lenis-stopped {
  overflow: hidden;
}

.lenis.lenis-scrolling iframe {
  pointer-events: none;
}

/* Additional fixes for mouse wheel scrolling */
html, body {
  overflow-x: hidden;
  overflow-y: auto;
  height: auto;
  touch-action: manipulation;
}

* {
  box-sizing: border-box;
}

/* Ensure proper wheel event handling */
html {
  scroll-behavior: auto !important;
}

/* Fix for potential conflicts */
body {
  overscroll-behavior: none;
}

/* Hero Text Color Fix */
.choice-text h2 {
  color: #0369A1 !important;
}

/* Hero Button Style Fix */
.hero-text button {
  background-color: #0369A1 !important;
  color: white !important;
  padding: 10px 20px !important;
  font-size: 14px !important;
  border-radius: 5px !important;
  border: none !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  width: auto !important;
  max-width: 250px !important;
}

.hero-text button:hover {
  background-color: #0284c7 !important;
}

/* Remove Slider Arrow Background */
.swiper-button-prev, 
.swiper-button-next {
  background: transparent !important;
}