/* Migration Benefits Styles */
.migration-benefits-section {
    padding: 100px 0;
    background: linear-gradient(135deg, #f8fafc 0%, #f0f9ff 100%);
    position: relative;
    z-index: 3;
}

.benefits-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30px;
    margin-bottom: 80px;
}

.benefit-card {
    background: #A5B3C0;
    border-radius: 15px;
    padding: 30px 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 128, 0.05);
    transition: all 0.3s ease;
    text-align: center;
    position: relative;
    overflow: hidden;
    z-index: 1;
    border: 1px solid rgba(0, 0, 128, 0.03);
}

.benefit-hover-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0, 0, 128, 0.03) 0%, rgba(59, 130, 246, 0.05) 100%);
    opacity: 0;
    transition: all 0.3s ease;
    z-index: -1;
}

.benefit-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 128, 0.1);
    border-color: rgba(59, 130, 246, 0.2);
}

.benefit-card:hover .benefit-hover-overlay {
    opacity: 1;
}

.benefit-icon-wrapper {
    position: relative;
    width: 90px;
    height: 90px;
    margin: 0 auto 25px;
}

.benefit-icon-wrapper::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(59, 130, 246, 0.1);
    border-radius: 50%;
    transform: scale(1.2);
    opacity: 0.5;
    transition: all 0.3s ease;
}

.benefit-card:hover .benefit-icon-wrapper::before {
    transform: scale(1.4);
    opacity: 0.7;
}

.benefit-icon {
    width: 80px;
    height: 80px;
    background: #f0f9ff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    position: relative;
    z-index: 2;
    box-shadow: 0 10px 20px rgba(59, 130, 246, 0.1);
    transition: all 0.3s ease;
}

.benefit-card:hover .benefit-icon {
    background: #e0f2fe;
    transform: scale(1.05);
    box-shadow: 0 15px 30px rgba(59, 130, 246, 0.2);
}

.benefit-icon i {
    font-size: 32px;
    color: #000080;
    transition: all 0.3s ease;
}

.benefit-card:hover .benefit-icon i {
    color: #3b82f6;
}

.benefit-card h3 {
    font-size: 1.3rem;
    color: #000080;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.benefit-card:hover h3 {
    color: #3b82f6;
}

.benefit-card p {
    color: #64748b;
    font-size: 0.95rem;
    line-height: 1.6;
}

/* Stats Section Styles */
.migration-stats-wrapper {
    background: white;
    border-radius: 20px;
    padding: 50px 40px;
    box-shadow: 0 15px 40px rgba(0, 0, 128, 0.08);
    position: relative;
    overflow: hidden;
    opacity: 1;
    transform: translateY(0);
    transition: all 0.8s ease;
}

.migration-stats-wrapper.visible {
    opacity: 1;
    transform: translateY(0);
}

.migration-stats-wrapper::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, #000080, #3b82f6);
}

.stats-header {
    text-align: center;
    margin-bottom: 40px;
}

.stats-header h3 {
    font-size: 1.8rem;
    color: #000080;
    margin-bottom: 10px;
}

.stats-header p {
    color: #64748b;
    font-size: 1.1rem;
    max-width: 700px;
    margin: 0 auto;
}

.migration-stats {
    display: flex;
    justify-content: space-around;
    align-items: center;
}

.stat-item {
    text-align: center;
    padding: 20px;
    position: relative;
    opacity: 1;
    transform: translateY(0);
    transition: all 0.5s ease;
}

.migration-stats-wrapper.visible .stat-item:nth-child(1) {
    opacity: 1;
    transform: translateY(0);
    transition-delay: 0.1s;
}

.migration-stats-wrapper.visible .stat-item:nth-child(2) {
    opacity: 1;
    transform: translateY(0);
    transition-delay: 0.3s;
}

.migration-stats-wrapper.visible .stat-item:nth-child(3) {
    opacity: 1;
    transform: translateY(0);
    transition-delay: 0.5s;
}

.stat-icon {
    width: 60px;
    height: 60px;
    background: #f0f9ff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    box-shadow: 0 10px 20px rgba(59, 130, 246, 0.1);
    opacity: 1;
    transform: scale(1);
    transition: all 0.5s ease 0.3s;
}

.migration-stats-wrapper.visible .stat-icon {
    opacity: 1;
    transform: scale(1);
}

.stat-icon i {
    font-size: 28px;
    color: #3b82f6;
}

.stat-number {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 5px;
    line-height: 1;
    display: inline-block;
    position: relative;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #000080, #3b82f6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
}

.stat-number.counted {
    animation: pulse 0.5s ease-out;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.migration-stats-wrapper.visible {
    opacity: 1;
    transform: translateY(0);
}

.stat-unit {
    font-size: 1.5rem;
    font-weight: 600;
    color: #3b82f6;
    margin-bottom: 15px;
}

.stat-label {
    color: #64748b;
    font-size: 1rem;
    font-weight: 500;
}

/* Responsive styles */
@media (max-width: 1200px) {
    .benefits-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 992px) {
    .benefits-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .migration-stats {
        flex-direction: column;
        gap: 40px;
    }
    
    .stat-item {
        width: 100%;
        max-width: 300px;
        margin: 0 auto;
    }
}

@media (max-width: 768px) {
    .benefits-grid {
        grid-template-columns: 1fr;
    }
    
    .migration-stats-wrapper {
        padding: 40px 20px;
    }
    
    .stat-number {
        font-size: 3rem;
    }
}