/*
 * SLIDER & MARQUEE TEXT FIX
 * Make text white on slider images and marquee images
 */

/* ========================================
   SLIDER TEXT - WHITE ON IMAGES
======================================== */

/* All text on slider images - WHITE with shadow for visibility */
.swiper-slide h1, .swiper-slide h2, .swiper-slide h3, .swiper-slide h4, .swiper-slide h5, .swiper-slide h6,
.swiper-slide p, .swiper-slide span, .swiper-slide div, .swiper-slide a,
.slider h1, .slider h2, .slider h3, .slider h4, .slider h5, .slider h6,
.slider p, .slider span, .slider div, .slider a,
.hero-slider h1, .hero-slider h2, .hero-slider h3, .hero-slider h4, .hero-slider h5, .hero-slider h6,
.hero-slider p, .hero-slider span, .hero-slider div, .hero-slider a,
.banner h1, .banner h2, .banner h3, .banner h4, .banner h5, .banner h6,
.banner p, .banner span, .banner div, .banner a,
.carousel-item h1, .carousel-item h2, .carousel-item h3, .carousel-item h4, .carousel-item h5, .carousel-item h6,
.carousel-item p, .carousel-item span, .carousel-item div, .carousel-item a {
    color: #FFFFFF !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.7) !important;
    font-weight: 600 !important;
    z-index: 10 !important;
    position: relative !important;
}

/* Slider buttons */
.swiper-slide button, .swiper-slide .btn, .swiper-slide [class*="btn"],
.slider button, .slider .btn, .slider [class*="btn"],
.hero-slider button, .hero-slider .btn, .hero-slider [class*="btn"],
.banner button, .banner .btn, .banner [class*="btn"],
.carousel-item button, .carousel-item .btn, .carousel-item [class*="btn"] {
    color: #FFFFFF !important;
    background-color: rgba(3, 105, 161, 0.9) !important;
    border: 2px solid #FFFFFF !important;
    text-shadow: none !important;
}

.swiper-slide button:hover, .swiper-slide .btn:hover, .swiper-slide [class*="btn"]:hover,
.slider button:hover, .slider .btn:hover, .slider [class*="btn"]:hover,
.hero-slider button:hover, .hero-slider .btn:hover, .hero-slider [class*="btn"]:hover,
.banner button:hover, .banner .btn:hover, .banner [class*="btn"]:hover,
.carousel-item button:hover, .carousel-item .btn:hover, .carousel-item [class*="btn"]:hover {
    background-color: #0369A1 !important;
    color: #FFFFFF !important;
    transform: translateY(-2px) !important;
}

/* ========================================
   MARQUEE TEXT - WHITE ON IMAGES
======================================== */

/* All text on marquee images - WHITE with shadow for visibility */
.marquee h1, .marquee h2, .marquee h3, .marquee h4, .marquee h5, .marquee h6,
.marquee p, .marquee span, .marquee div, .marquee a,
.marquee-content h1, .marquee-content h2, .marquee-content h3, .marquee-content h4, .marquee-content h5, .marquee-content h6,
.marquee-content p, .marquee-content span, .marquee-content div, .marquee-content a,
.marquee-item h1, .marquee-item h2, .marquee-item h3, .marquee-item h4, .marquee-item h5, .marquee-item h6,
.marquee-item p, .marquee-item span, .marquee-item div, .marquee-item a,
.scrolling-images h1, .scrolling-images h2, .scrolling-images h3, .scrolling-images h4, .scrolling-images h5, .scrolling-images h6,
.scrolling-images p, .scrolling-images span, .scrolling-images div, .scrolling-images a,
.image-marquee h1, .image-marquee h2, .image-marquee h3, .image-marquee h4, .image-marquee h5, .image-marquee h6,
.image-marquee p, .image-marquee span, .image-marquee div, .image-marquee a {
    color: #FFFFFF !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.7) !important;
    font-weight: 600 !important;
    z-index: 10 !important;
    position: relative !important;
}

/* Marquee buttons */
.marquee button, .marquee .btn, .marquee [class*="btn"],
.marquee-content button, .marquee-content .btn, .marquee-content [class*="btn"],
.marquee-item button, .marquee-item .btn, .marquee-item [class*="btn"],
.scrolling-images button, .scrolling-images .btn, .scrolling-images [class*="btn"],
.image-marquee button, .image-marquee .btn, .image-marquee [class*="btn"] {
    color: #FFFFFF !important;
    background-color: rgba(3, 105, 161, 0.9) !important;
    border: 2px solid #FFFFFF !important;
    text-shadow: none !important;
}

.marquee button:hover, .marquee .btn:hover, .marquee [class*="btn"]:hover,
.marquee-content button:hover, .marquee-content .btn:hover, .marquee-content [class*="btn"]:hover,
.marquee-item button:hover, .marquee-item .btn:hover, .marquee-item [class*="btn"]:hover,
.scrolling-images button:hover, .scrolling-images .btn:hover, .scrolling-images [class*="btn"]:hover,
.image-marquee button:hover, .image-marquee .btn:hover, .image-marquee [class*="btn"]:hover {
    background-color: #0369A1 !important;
    color: #FFFFFF !important;
    transform: translateY(-2px) !important;
}

/* ========================================
   ADDITIONAL IMAGE OVERLAY TEXT
======================================== */

/* Any text over background images */
[style*="background-image"] h1, [style*="background-image"] h2, [style*="background-image"] h3, 
[style*="background-image"] h4, [style*="background-image"] h5, [style*="background-image"] h6,
[style*="background-image"] p, [style*="background-image"] span, [style*="background-image"] div,
[style*="background-image"] a, [style*="background-image"] button,
.bg-image h1, .bg-image h2, .bg-image h3, .bg-image h4, .bg-image h5, .bg-image h6,
.bg-image p, .bg-image span, .bg-image div, .bg-image a, .bg-image button,
.image-overlay h1, .image-overlay h2, .image-overlay h3, .image-overlay h4, .image-overlay h5, .image-overlay h6,
.image-overlay p, .image-overlay span, .image-overlay div, .image-overlay a, .image-overlay button {
    color: #FFFFFF !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.7) !important;
    font-weight: 600 !important;
    z-index: 10 !important;
    position: relative !important;
}

/* ========================================
   SPECIFIC SECTIONS WITH IMAGES
======================================== */

/* CTA sections with background images */
.cta-section h1, .cta-section h2, .cta-section h3, .cta-section h4, .cta-section h5, .cta-section h6,
.cta-section p, .cta-section span, .cta-section div, .cta-section a,
.apply-visa-cta h1, .apply-visa-cta h2, .apply-visa-cta h3, .apply-visa-cta h4, .apply-visa-cta h5, .apply-visa-cta h6,
.apply-visa-cta p, .apply-visa-cta span, .apply-visa-cta div, .apply-visa-cta a {
    color: #FFFFFF !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.7) !important;
    font-weight: 600 !important;
    z-index: 10 !important;
    position: relative !important;
}

/* Image sections */
.image-section h1, .image-section h2, .image-section h3, .image-section h4, .image-section h5, .image-section h6,
.image-section p, .image-section span, .image-section div, .image-section a {
    color: #FFFFFF !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.7) !important;
    font-weight: 600 !important;
    z-index: 10 !important;
    position: relative !important;
}

/* Ensure text is always visible on any image background */
.has-background-image h1, .has-background-image h2, .has-background-image h3, 
.has-background-image h4, .has-background-image h5, .has-background-image h6,
.has-background-image p, .has-background-image span, .has-background-image div,
.has-background-image a {
    color: #FFFFFF !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.7) !important;
    font-weight: 600 !important;
    z-index: 10 !important;
    position: relative !important;
}
