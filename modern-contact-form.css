/* MODERN CONTACT FORM - BEAUTIFUL UI DESIGN WITH ANIMATIONS */

/* Keyframe Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(50px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* Contact Section Background - Visit Page Colors */
.contact-card-section {
    background: linear-gradient(135deg, #687FE5 0%, #5a6fd8 100%);
    padding: 80px 20px;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.contact-card-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="60" cy="10" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="10" cy="60" r="1.5" fill="rgba(255,255,255,0.1)"/></svg>');
    pointer-events: none;
}

/* Card Wrapper */
.card-wrapper {
    max-width: 900px;
    width: 100%;
    position: relative;
    z-index: 1;
}

.card-wrapper h2 {
    text-align: center;
    color: white;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 50px;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    letter-spacing: -0.5px;
    animation: fadeInUp 1s ease-out;
    font-family: 'Inter', sans-serif;
}

/* Modern Contact Card */
.contact-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 25px;
    padding: 50px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15),
                0 10px 25px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
    animation: slideInUp 1.2s ease-out 0.3s both;
}

.contact-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(90deg, #687FE5 0%, #5a6fd8 100%);
}

/* Form Layout */
#visaContact {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    align-items: start;
}

/* Field Styling with Animations */
.field {
    display: flex;
    flex-direction: column;
    position: relative;
    animation: fadeInUp 0.8s ease-out both;
}

.field:nth-child(odd) {
    animation: fadeInLeft 0.8s ease-out both;
}

.field:nth-child(even) {
    animation: fadeInRight 0.8s ease-out both;
}

.field.full {
    grid-column: 1 / -1;
    animation: fadeInUp 0.8s ease-out both;
}

/* Staggered Animation Delays */
.field:nth-child(1) { animation-delay: 0.1s; }
.field:nth-child(2) { animation-delay: 0.2s; }
.field:nth-child(3) { animation-delay: 0.3s; }
.field:nth-child(4) { animation-delay: 0.4s; }
.field:nth-child(5) { animation-delay: 0.5s; }
.field:nth-child(6) { animation-delay: 0.6s; }
.field:nth-child(7) { animation-delay: 0.7s; }
.field:nth-child(8) { animation-delay: 0.8s; }
.field:nth-child(9) { animation-delay: 0.9s; }
.field:nth-child(10) { animation-delay: 1s; }
.field:nth-child(11) { animation-delay: 1.1s; }
.field:nth-child(12) { animation-delay: 1.2s; }
.field:nth-child(13) { animation-delay: 1.3s; }

.field label {
    font-size: 14px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.field label::before {
    content: '';
    width: 4px;
    height: 4px;
    background: #687FE5;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

/* Input Container */
.input-container {
    position: relative;
    display: flex;
    align-items: center;
    background: #f8fafc;
    border: 2px solid #e2e8f0;
    border-radius: 15px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
}

.input-container:hover {
    border-color: #cbd5e1;
    background: #f1f5f9;
}

.input-container:focus-within {
    border-color: #687FE5;
    background: white;
    box-shadow: 0 0 0 4px rgba(104, 127, 229, 0.1);
    transform: translateY(-2px);
}

/* Input Icon */
.input-icon {
    padding: 0 15px;
    color: #64748b;
    font-size: 18px;
    transition: color 0.3s ease;
    background: #e2e8f0;
    height: 100%;
    display: flex;
    align-items: center;
    border-radius: 13px 0 0 13px;
}

.input-container:focus-within .input-icon {
    color: #687FE5;
    background: #687FE5;
    color: white;
}

/* Input Fields */
.input-container input,
.input-container select,
.input-container textarea {
    flex: 1;
    border: none;
    outline: none;
    padding: 16px 20px;
    font-size: 16px;
    background: transparent;
    color: #374151;
    font-family: inherit;
}

.input-container input::placeholder,
.input-container textarea::placeholder {
    color: #9ca3af;
    font-weight: 400;
}

/* Select Styling */
.input-container select {
    cursor: pointer;
    appearance: none;
    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="%23687FE5" stroke-width="2"><polyline points="6,9 12,15 18,9"></polyline></svg>');
    background-repeat: no-repeat;
    background-position: right 15px center;
    background-size: 16px;
    padding-right: 45px;
}

/* Textarea Styling */
.input-container textarea {
    resize: vertical;
    min-height: 80px;
    font-family: inherit;
}

/* File Input Styling */
.file-input {
    position: relative;
    cursor: pointer;
    min-height: 60px;
}

.file-input input[type="file"] {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.file-text {
    flex: 1;
    padding: 16px 20px;
    color: #9ca3af;
    font-size: 16px;
    display: flex;
    align-items: center;
}

.file-input:hover .file-text {
    color: #687FE5;
}

/* Submit Button with Visit Page Colors */
.btn-submit {
    grid-column: 1 / -1;
    background: linear-gradient(135deg, #687FE5 0%, #5a6fd8 100%);
    color: white;
    border: none;
    padding: 18px 40px;
    border-radius: 15px;
    font-size: 18px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-top: 20px;
    box-shadow: 0 10px 25px rgba(104, 127, 229, 0.3);
    position: relative;
    overflow: hidden;
    animation: fadeInUp 1s ease-out 1.5s both;
    font-family: 'Inter', sans-serif;
}

.btn-submit::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-submit:hover::before {
    left: 100%;
}

.btn-submit:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(104, 127, 229, 0.4);
    background: linear-gradient(135deg, #5a6fd8 0%, #4f63d2 100%);
}

.btn-submit:active {
    transform: translateY(-1px);
}

/* Error Messages */
.error-message {
    color: #ef4444;
    font-size: 12px;
    margin-top: 5px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.field.error .error-message {
    opacity: 1;
}

.field.error .input-container {
    border-color: #ef4444;
    background: #fef2f2;
}

/* Small Text */
small {
    color: #6b7280;
    font-size: 12px;
    margin-top: 5px;
    display: block;
}

/* Responsive Design */
@media (max-width: 768px) {
    .contact-card-section {
        padding: 40px 15px;
    }
    
    .card-wrapper h2 {
        font-size: 2rem;
        margin-bottom: 30px;
    }
    
    .contact-card {
        padding: 30px 25px;
        border-radius: 20px;
    }
    
    #visaContact {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .input-container input,
    .input-container select,
    .input-container textarea {
        padding: 14px 16px;
        font-size: 15px;
    }
    
    .btn-submit {
        padding: 16px 30px;
        font-size: 16px;
    }
}

@media (max-width: 480px) {
    .contact-card {
        padding: 25px 20px;
    }
    
    .input-icon {
        padding: 0 12px;
        font-size: 16px;
    }
    
    .input-container input,
    .input-container select,
    .input-container textarea {
        padding: 12px 15px;
        font-size: 14px;
    }
}
