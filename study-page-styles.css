/*
 * STUDY PAGE SPECIFIC STYLES
 * White Wings Visa - Study Page Only CSS
 * Contains all styles specific to study.html
 */

/* Import all required styles for study page */
@import url('style.css');
@import url('professional-mobile-menu.css');
@import url('style-fix.css');
@import url('loader.css');
@import url('pages.css');
@import url('professional-study.css');
@import url('study-sections.css');
@import url('education-services.css');
@import url('compact-journey.css');
@import url('coaching-section.css');
@import url('study-opportunity-cards.css');
@import url('university-placements.css');
@import url('hero-buttons.css');
@import url('simple-form.css');
@import url('master-mobile-navbar.css');
@import url('apply-now-button.css');
@import url('white-wings-colors.css');
@import url('text-visibility-fix.css');
@import url('white-wings-design-system.css');
@import url('button-icon-overrides.css');

/* Study page specific overrides and enhancements */
.study-hero-section {
    /* Study page hero specific styles */
}

.study-services-section {
    /* Study page services section specific styles */
}

.study-process-section {
    /* Study page process section specific styles */
}

.study-universities-section {
    /* Study page universities section specific styles */
}

.study-assessment-section {
    /* Study page assessment section specific styles */
}

/* Study page specific responsive styles */
@media (max-width: 768px) {
    .study-hero-section {
        /* Mobile specific study hero styles */
    }
    
    .study-services-section {
        /* Mobile specific study services styles */
    }
}

/* Study page specific animations */
@keyframes study-fade-up {
    from { opacity: 0; transform: translateY(30px); }
    to { opacity: 1; transform: translateY(0); }
}

.study-animate {
    animation: study-fade-up 0.8s ease-out;
}
