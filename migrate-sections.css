/* Additional Migrate Sections Styles */

/* Check Eligibility Section */
.check-eligibility-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    position: relative;
    z-index: 3;
    overflow: hidden;
}

.check-eligibility-section::before {
    content: "";
    position: absolute;
    top: -100px;
    right: -100px;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background: rgba(3, 105, 161, 0.05);
    z-index: 1;
}

.check-eligibility-section::after {
    content: "";
    position: absolute;
    bottom: -150px;
    left: -150px;
    width: 400px;
    height: 400px;
    border-radius: 50%;
    background: rgba(3, 105, 161, 0.05);
    z-index: 1;
}

.check-eligibility-section .light-overlay {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    z-index: 1;
}

.eligibility-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 2;
}

.eligibility-content {
    display: flex;
    align-items: center;
    gap: 60px;
}

.eligibility-left {
    flex: 1;
}

.eligibility-image-container {
    position: relative;
    width: 100%;
}

.eligibility-left img {
    width: 100%;
    height: 450px;
    object-fit: cover;
    border-radius: 15px;
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.2);
}

.eligibility-badge {
    position: absolute;
    bottom: -20px;
    right: -20px;
    background: white;
    padding: 15px 20px;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    gap: 10px;
}

.badge-icon {
    width: 40px;
    height: 40px;
    background: #e0f2fe;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.badge-icon i {
    font-size: 20px;
    color: #0369a1;
}

.badge-text {
    font-weight: 700;
    color: #0f172a;
    font-size: 1.1rem;
}

.eligibility-right {
    flex: 1;
    color: #334155;
}

.eligibility-right .section-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: #e0f2fe;
    color: #0369a1;
    padding: 8px 16px;
    border-radius: 50px;
    margin-bottom: 20px;
    font-weight: 600;
}

.eligibility-right .section-badge i {
    font-size: 1.2rem;
}

.eligibility-right h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
    line-height: 1.2;
    color: #0f172a;
}

.eligibility-right p {
    font-size: 1.1rem;
    margin-bottom: 30px;
    color: #64748b;
    line-height: 1.6;
}

.eligibility-features {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;
}

.eligibility-feature {
    display: flex;
    align-items: center;
    gap: 10px;
}

.eligibility-feature i {
    font-size: 20px;
    color: #0369a1;
}

.eligibility-feature span {
    font-weight: 600;
    color: #334155;
}

.eligibility-btn {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    padding: 15px 30px;
    background: #0369a1;
    color: white;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    box-shadow: 0 10px 30px rgba(3, 105, 161, 0.2);
}

.eligibility-btn i {
    transition: all 0.3s ease;
}

.eligibility-btn:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(3, 105, 161, 0.3);
    background: #0284c7;
}

.eligibility-btn:hover i {
    transform: translateX(5px);
}

/* Get Evaluated Section */
.get-evaluated-section {
    padding: 80px 0;
    background: #fff;
    position: relative;
    z-index: 3;
}

.evaluated-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.evaluated-content {
    display: flex;
    gap: 40px;
}

.evaluated-left {
    flex: 2;
}

.evaluated-left h2 {
    font-size: 2.5rem;
    color: #000080;
    margin-bottom: 20px;
}

.evaluated-left p {
    font-size: 1.1rem;
    color: #64748b;
    margin-bottom: 30px;
    max-width: 90%;
}

.evaluation-features {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin-bottom: 30px;
}

.eval-feature {
    display: flex;
    align-items: center;
    gap: 15px;
}

.eval-icon {
    width: 50px;
    height: 50px;
    background: #f0f9ff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.eval-icon i {
    font-size: 24px;
    color: #3b82f6;
}

.eval-feature span {
    font-weight: 600;
    color: #334155;
}

.get-evaluated-btn {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    padding: 15px 30px;
    background: #000080;
    color: white;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.get-evaluated-btn:hover {
    background: #3b82f6;
    transform: translateY(-5px);
}

.evaluated-right {
    flex: 1;
}

.did-you-know {
    background: #f8fafc;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 128, 0.05);
}

.bulb-icon {
    width: 60px;
    height: 60px;
    background: #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 128, 0.1);
}

.bulb-icon i {
    font-size: 30px;
    color: #fbbf24;
}

.did-you-know h3 {
    font-size: 1.5rem;
    color: #000080;
    margin-bottom: 15px;
}

.did-you-know p {
    font-size: 1rem;
    color: #64748b;
    margin-bottom: 20px;
}

.news-links {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.news-links a {
    color: #3b82f6;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.news-links a:hover {
    color: #000080;
    text-decoration: underline;
}

/* Contact CTA Section - Enhanced with Background Image */
.contact-cta-section {
    padding: 120px 0;
    background: url('images/hero/hands-holding-tickets-close-up.jpg') center/cover no-repeat;
    text-align: center;
    color: white;
    position: relative;
    z-index: 3;
    overflow: hidden;
}

.contact-cta-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    z-index: 1;
}



.contact-cta-container {
    max-width: 900px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 3;
    background: rgba(30, 64, 175, 0.15);
    backdrop-filter: blur(20px);
    border-radius: 25px;
    padding: 60px 40px;
    border: 1px solid rgba(30, 64, 175, 0.2);
    box-shadow: 0 25px 50px rgba(30, 64, 175, 0.2);
}

.contact-cta-section h2 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 25px;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
    background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: fadeInUp 0.8s ease-out;
}

.contact-cta-section p {
    font-size: 1.3rem;
    margin-bottom: 40px;
    opacity: 0.95;
    line-height: 1.6;
    text-shadow: 0 1px 5px rgba(0, 0, 0, 0.3);
    animation: fadeInUp 0.8s ease-out 0.2s both;
}

.cta-button {
    display: inline-flex;
    align-items: center;
    gap: 12px;
    padding: 18px 40px;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    color: #1e40af;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 700;
    font-size: 1.1rem;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 15px 35px rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    text-transform: uppercase;
    letter-spacing: 1px;
    position: relative;
    overflow: hidden;
    animation: fadeInUp 0.8s ease-out 0.4s both;
}

.cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(30, 64, 175, 0.2), transparent);
    transition: left 0.6s;
}

.cta-button:hover::before {
    left: 100%;
}

.cta-button:hover {
    transform: translateY(-8px) scale(1.05);
    box-shadow: 0 25px 50px rgba(255, 255, 255, 0.4);
    background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
    color: #1e40af;
}

/* Responsive styles */
@media (max-width: 992px) {
    .eligibility-content,
    .evaluated-content {
        flex-direction: column;
    }
    
    .evaluation-features {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .eligibility-right h2,
    .evaluated-left h2,
    .contact-cta-section {
        padding: 80px 0;
    }

    .contact-cta-container {
        padding: 40px 30px;
        border-radius: 20px;
    }

    .contact-cta-section h2 {
        font-size: 2.2rem;
    }

    .contact-cta-section p {
        font-size: 1.1rem;
        margin-bottom: 30px;
    }

    .cta-button {
        padding: 16px 35px;
        font-size: 1rem;
    }
}

@media (max-width: 768px) {
    .contact-cta-section {
        padding: 60px 0;
    }

    .contact-cta-container {
        padding: 30px 20px;
        border-radius: 15px;
        margin: 0 15px;
    }

    .contact-cta-section h2 {
        font-size: 1.8rem;
        margin-bottom: 20px;
    }

    .contact-cta-section p {
        font-size: 1rem;
        margin-bottom: 25px;
    }

    .cta-button {
        padding: 14px 30px;
        font-size: 0.9rem;
        letter-spacing: 0.5px;
    }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
    
    .eligibility-features {
        flex-wrap: wrap;
    }
    
    .eligibility-feature {
        width: 45%;
    }
}

@media (max-width: 768px) {
    .eligibility-badge {
        bottom: -15px;
        right: 20px;
        padding: 10px 15px;
    }
    
    .badge-icon {
        width: 30px;
        height: 30px;
    }
    
    .badge-text {
        font-size: 0.9rem;
    }
    
    .eligibility-features {
        flex-direction: column;
        gap: 15px;
    }
    
    .eligibility-feature {
        width: 100%;
    }
}

@media (max-width: 768px) {
    .evaluation-features {
        grid-template-columns: 1fr;
    }
}