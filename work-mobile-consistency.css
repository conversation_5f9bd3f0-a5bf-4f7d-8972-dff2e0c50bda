/*
 * WORK PAGE MOBILE CONSISTENCY
 * Ensure all desktop changes appear exactly same on mobile
 */

/* Mobile Responsive - All Changes Consistent */
@media (max-width: 1200px) {
    /* Work Opportunity Section */
    .work-opportunity-section {
        padding: 80px 0 !important;
    }
    
    .work-destinations-grid {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 25px !important;
    }
    
    /* Cards maintain same styling */
    .work-destination-card {
        border-radius: 20px !important;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1) !important;
        transition: all 0.4s ease !important;
        overflow: hidden !important;
        border: 1px solid rgba(0, 0, 0, 0.05) !important;
    }
    
    .work-destination-card:hover {
        transform: translateY(-15px) !important;
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15) !important;
    }
    
    /* Header images maintain quality */
    .work-destination-card .destination-header {
        height: 180px !important;
        background-size: cover !important;
        background-position: center !important;
        border-radius: 20px 20px 0 0 !important;
    }
    
    /* Icons and flags maintain positioning */
    .work-destination-card .destination-icon {
        position: absolute !important;
        top: 15px !important;
        right: 15px !important;
        background: rgba(255, 255, 255, 0.9) !important;
        width: 45px !important;
        height: 45px !important;
        border-radius: 50% !important;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2) !important;
    }
    
    .work-destination-card .destination-flag {
        position: absolute !important;
        top: -12px !important;
        left: 20px !important;
        background: white !important;
        padding: 6px !important;
        border-radius: 50% !important;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15) !important;
        width: 55px !important;
        height: 55px !important;
    }
}

@media (max-width: 768px) {
    /* Single column layout */
    .work-destinations-grid {
        grid-template-columns: 1fr !important;
        gap: 20px !important;
    }
    
    /* Cards maintain enhanced styling */
    .work-destination-card {
        border-radius: 20px !important;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1) !important;
        transition: all 0.4s ease !important;
        overflow: hidden !important;
        border: 1px solid rgba(0, 0, 0, 0.05) !important;
        margin: 0 10px !important;
    }
    
    .work-destination-card:hover {
        transform: translateY(-10px) !important;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15) !important;
    }
    
    /* Header images maintain quality on mobile */
    .work-destination-card .destination-header {
        height: 160px !important;
        background-size: cover !important;
        background-position: center !important;
        border-radius: 20px 20px 0 0 !important;
        padding: 20px !important;
    }
    
    .work-destination-card .destination-header::before {
        background: linear-gradient(to bottom, rgba(0,0,0,0.1), rgba(0,0,0,0.6)) !important;
    }
    
    /* Icons maintain visibility */
    .work-destination-card .destination-icon {
        position: absolute !important;
        top: 15px !important;
        right: 15px !important;
        background: rgba(255, 255, 255, 0.9) !important;
        width: 40px !important;
        height: 40px !important;
        border-radius: 50% !important;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2) !important;
    }
    
    .work-destination-card .destination-icon i {
        font-size: 1.2rem !important;
    }
    
    /* Flags maintain positioning */
    .work-destination-card .destination-flag {
        position: absolute !important;
        top: -10px !important;
        left: 20px !important;
        background: white !important;
        padding: 5px !important;
        border-radius: 50% !important;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15) !important;
        width: 50px !important;
        height: 50px !important;
    }
    
    .work-destination-card .destination-flag img {
        width: 30px !important;
    }
    
    /* Content area maintains spacing */
    .work-destination-card .destination-content {
        padding: 25px 20px 20px !important;
    }
    
    /* Feature tags maintain styling */
    .work-destination-card .feature-tag {
        border-radius: 15px !important;
        transition: all 0.3s ease !important;
        font-size: 0.75rem !important;
        padding: 5px 10px !important;
    }
    
    .work-destination-card:hover .feature-tag {
        transform: translateY(-2px) !important;
    }
    
    /* Buttons maintain styling */
    .work-destination-card .destination-btn {
        border-radius: 25px !important;
        transition: all 0.3s ease !important;
        padding: 10px 18px !important;
        font-size: 0.85rem !important;
    }
    
    .work-destination-card .destination-btn:hover {
        transform: translateY(-2px) !important;
    }
    
    /* Text maintains readability */
    .work-destination-card .destination-header h3 {
        font-size: 1.4rem !important;
        font-weight: 700 !important;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5) !important;
    }
    
    .work-destination-card p {
        line-height: 1.6 !important;
        font-size: 0.9rem !important;
    }
}

@media (max-width: 480px) {
    /* Extra small screens */
    .work-opportunity-section {
        padding: 60px 0 !important;
    }
    
    .work-destinations-grid {
        gap: 15px !important;
        padding: 0 5px !important;
    }
    
    /* Cards maintain quality on small screens */
    .work-destination-card {
        border-radius: 18px !important;
        margin: 0 5px !important;
    }
    
    .work-destination-card .destination-header {
        height: 140px !important;
        padding: 15px !important;
        border-radius: 18px 18px 0 0 !important;
    }
    
    .work-destination-card .destination-header h3 {
        font-size: 1.2rem !important;
    }
    
    .work-destination-card .destination-icon {
        width: 35px !important;
        height: 35px !important;
        top: 12px !important;
        right: 12px !important;
    }
    
    .work-destination-card .destination-icon i {
        font-size: 1rem !important;
    }
    
    .work-destination-card .destination-flag {
        width: 45px !important;
        height: 45px !important;
        top: -8px !important;
        left: 15px !important;
    }
    
    .work-destination-card .destination-flag img {
        width: 25px !important;
    }
    
    .work-destination-card .destination-content {
        padding: 20px 15px 15px !important;
    }
    
    .work-destination-card .feature-tag {
        font-size: 0.7rem !important;
        padding: 4px 8px !important;
    }
    
    .work-destination-card .destination-btn {
        padding: 8px 15px !important;
        font-size: 0.8rem !important;
    }
    
    .work-destination-card p {
        font-size: 0.85rem !important;
    }
}

/* Ensure all hover effects work on mobile */
@media (hover: hover) {
    .work-destination-card:hover {
        transform: translateY(-15px) !important;
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15) !important;
    }
    
    .work-destination-card:hover .feature-tag {
        transform: translateY(-2px) !important;
    }
    
    .work-destination-card .destination-btn:hover {
        transform: translateY(-2px) !important;
    }
}

/* Touch devices - maintain visual feedback */
@media (hover: none) {
    .work-destination-card:active {
        transform: translateY(-5px) !important;
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1) !important;
    }
}
