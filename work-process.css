/* Work Process Styles */
.work-process-section {
    padding: 100px 0;
    background: #fff;
    position: relative;
    z-index: 3;
}

.work-process-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.work-process-steps {
    position: relative;
    margin: 60px 0;
    padding: 0 20px;
}

.work-process-steps::before {
    content: "";
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 4px;
    height: 100%;
    background: linear-gradient(to bottom, #A3B0BD, #727272);
    border-radius: 4px;
}

.work-step {
    position: relative;
    margin-bottom: 80px;
    display: flex;
    align-items: center;
}

.work-step:nth-child(odd) {
    flex-direction: row-reverse;
}

.work-step:last-child {
    margin-bottom: 0;
}

.step-number {
    width: 60px;
    height: 60px;
    border-color: #000080;
    background: #A3B0BD;
    color: #222;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 700;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    z-index: 2;
    box-shadow: 0 5px 15px rgba(0, 0, 128, 0.2);
    border: 4px solid white;
}

.step-content {
    width: 45%;
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 128, 0.05);
    transition: all 0.3s ease;
}

.work-step:nth-child(odd) .step-content {
    margin-right: 30px;
}

.work-step:nth-child(even) .step-content {
    margin-left: 30px;
}

.step-content:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 128, 0.1);
}

.step-content h3 {
    font-size: 1.4rem;
    color: #000080;
    margin-bottom: 15px;
}

.step-content p {
    color: #64748b;
    line-height: 1.6;
}

/* Responsive styles */
@media (max-width: 992px) {
    .work-process-steps::before {
        left: 30px;
    }
    
    .work-step {
        flex-direction: row !important;
    }
    
    .step-number {
        left: 30px;
        transform: none;
    }
    
    .step-content {
        width: calc(100% - 60px);
        margin-left: 60px !important;
    }
}

@media (max-width: 768px) {
    .work-process-section {
        padding: 60px 0;
    }
    
    .work-process-steps {
        margin: 40px 0;
    }
    
    .step-content {
        padding: 20px;
    }
    
    .step-content h3 {
        font-size: 1.2rem;
    }
}