<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Us - White Wings Visa Consultancy Pune</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="professional-mobile-menu.css">
    <link rel="stylesheet" href="style-fix.css">
    <link rel="stylesheet" href="pages.css">
    <link rel="stylesheet" href="contact-enhanced.css">
    <link rel="stylesheet" href="universal-mobile-fix.css">
    <link rel="stylesheet" href="apply-now-button.css">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@4.5.0/fonts/remixicon.css" rel="stylesheet" />

    <!-- White Wings Custom Color Scheme -->
    <link rel="stylesheet" href="white-wings-colors.css">


    <!-- Slider & Marquee Text Fix -->

    <link rel="stylesheet" href="slider-text-fix.css">
    <!-- Contact Form Fix -->
    <link rel="stylesheet" href="contact-form-fix.css">



    <!-- Gilroy Font -->
    <link href="https://fonts.cdnfonts.com/css/gilroy-bold" rel="stylesheet">

    <!-- Enhanced Form Validation -->
    <script src="enhanced-form-validation.js" defer></script>

    <style>
        /* PROFESSIONAL CONTACT PAGE REDESIGN */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            line-height: 1.6;
            color: #1e293b;
            overflow-x: hidden;
        }

        /* HERO SECTION - MUCH LIGHTER BACKGROUND & COMPACT HEIGHT */
        .contact-hero-section {
            background: linear-gradient(135deg, #60a5fa 0%, #3b82f6 50%, #2563eb 100%);
            position: relative;
            padding: 60px 0 40px;
            overflow: hidden;
            min-height: 50vh;
            display: flex;
            align-items: center;
        }



        .contact-hero-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
            position: relative;
            z-index: 2;
        }

        .contact-hero-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 80px;
            align-items: center;
        }

        .contact-hero-text {
            color: white;
        }

        .contact-hero-badge {
            display: inline-flex;
            align-items: center;
            gap: 10px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 12px 24px;
            border-radius: 50px;
            margin-bottom: 30px;
            font-size: 14px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            animation: fadeInUp 0.8s ease-out;
        }

        .contact-hero-badge i {
            font-size: 18px;
            color: #60a5fa;
        }

        .contact-hero-text h1 {
            font-size: 3.2rem;
            font-weight: 400;
            line-height: 1.3;
            margin-bottom: 30px;
            color: white;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
            animation: fadeInUp 1s ease-out 0.2s both;
            text-align: left;
        }

        .contact-hero-description {
            margin-bottom: 40px;
        }

        .contact-hero-description p {
            font-size: 1.2rem;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 20px;
            line-height: 1.7;
            animation: fadeInUp 1s ease-out 0.4s both;
        }

        .contact-hero-actions {
            display: flex;
            gap: 20px;
            animation: fadeInUp 1s ease-out 0.6s both;
        }

        .contact-btn-primary, .contact-btn-secondary {
            padding: 16px 32px;
            border-radius: 50px;
            font-weight: 600;
            font-size: 1rem;
            display: inline-flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            text-decoration: none;
            position: relative;
            overflow: hidden;
        }

        .contact-btn-primary {
            background: rgba(255, 255, 255, 0.95);
            color: #1d4ed8;
            border: 2px solid rgba(255, 255, 255, 0.8);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
        }

        .contact-btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
            background: rgba(255, 255, 255, 1);
            color: #1e40af;
        }

        .contact-btn-secondary {
            background: rgba(255, 255, 255, 0.15);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.4);
            backdrop-filter: blur(20px);
        }

        .contact-btn-secondary:hover {
            background: rgba(255, 255, 255, 0.25);
            border-color: rgba(255, 255, 255, 0.6);
            transform: translateY(-3px);
        }

        .contact-hero-visual {
            position: relative;
            animation: fadeInUp 1s ease-out 0.8s both;
        }

        .contact-hero-image-container {
            position: relative;
            border-radius: 30px;
            overflow: hidden;
            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.4);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .contact-hero-image {
            width: 100%;
            height: auto;
            display: block;
            transition: transform 0.5s ease;
        }

        .contact-hero-image-container:hover .contact-hero-image {
            transform: scale(1.05);
        }

        .contact-image-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 30px;
            background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
        }

        .contact-overlay-badge {
            display: inline-flex;
            align-items: center;
            gap: 10px;
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(20px);
            padding: 12px 20px;
            border-radius: 50px;
            color: white;
            font-weight: 600;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .contact-overlay-badge i {
            font-size: 20px;
            color: #60a5fa;
        }

        /* ENHANCED FEATURES SECTION */
        .features-area-enhanced {
            padding: 120px 0;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            position: relative;
            overflow: hidden;
        }

        .features-area-enhanced::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="1" fill="%233b82f6" opacity="0.1"/></svg>') repeat;
            z-index: 1;
        }

        .features-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
            position: relative;
            z-index: 2;
        }

        .features-header {
            text-align: center;
            margin-bottom: 80px;
        }

        .codex-section-title h6 {
            display: inline-block;
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            padding: 12px 24px;
            border-radius: 50px;
            font-size: 14px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 1.5px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(59, 130, 246, 0.3);
        }

        .codex-section-title h2 {
            font-size: 3.5rem;
            font-weight: 200;
            color: #64748b;
            margin-bottom: 20px;
            line-height: 1.1;
        }

        .codex-section-title p {
            font-size: 1.3rem;
            color: #64748b;
            max-width: 800px;
            margin: 0 auto;
            line-height: 1.6;
        }

        .features-grid-enhanced {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 25px;
            max-width: 1400px;
            margin: 0 auto;
        }

        .feature-card {
            background: white;
            border-radius: 20px;
            padding: 30px 20px;
            text-align: center;
            position: relative;
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border: 2px solid #e2e8f0;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.06);
            min-height: 280px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(30, 64, 175, 0.15);
            border-color: #1e40af;
        }

        .feature-card:hover .feature-overlay {
            opacity: 1;
        }

        .feature-card:hover .feature-icon {
            transform: scale(1.1);
            background: linear-gradient(135deg, #1e40af, #1d4ed8);
            box-shadow: 0 10px 25px rgba(30, 64, 175, 0.3);
        }

        .feature-card:hover .feature-icon i {
            color: white;
            transform: scale(1.05);
        }

        .feature-card:hover .feature-content h3 {
            color: #1e40af;
        }

        .feature-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(30, 64, 175, 0.03), rgba(29, 78, 216, 0.03));
            opacity: 0;
            transition: all 0.4s ease;
            z-index: 1;
        }

        .feature-icon {
            width: 70px;
            height: 70px;
            background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
            border-radius: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            z-index: 2;
            border: 2px solid #e2e8f0;
        }

        .feature-icon i {
            font-size: 2rem;
            color: #1e40af;
            transition: all 0.4s ease;
        }

        .feature-content {
            position: relative;
            z-index: 2;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .feature-content h3 {
            font-size: 1.3rem;
            font-weight: 600;
            color: #1e40af;
            margin-bottom: 12px;
            transition: all 0.3s ease;
        }

        .feature-content p {
            color: #64748b;
            line-height: 1.6;
            margin-bottom: 20px;
            font-size: 0.9rem;
            flex-grow: 1;
        }

        .feature-stats {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 6px;
            padding: 15px;
            background: linear-gradient(135deg, #f8fafc, #f1f5f9);
            border-radius: 12px;
            border: 1px solid #e2e8f0;
            transition: all 0.3s ease;
            margin-top: auto;
        }

        .feature-card:hover .feature-stats {
            background: linear-gradient(135deg, rgba(30, 64, 175, 0.08), rgba(29, 78, 216, 0.08));
            border-color: #1e40af;
            transform: translateY(-3px);
        }

        .stat-number {
            font-size: 1.8rem;
            font-weight: 800;
            color: #1e40af;
            text-shadow: 0 1px 3px rgba(30, 64, 175, 0.2);
        }

        .stat-label {
            font-size: 0.8rem;
            color: #64748b;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* MODERN CONTACT FORM SECTION WITH BACKGROUND IMAGE - COMPACT */
        .contact-card-section {
            background: url('https://its-poland.com/files/services_photos/09c364b50edd87b86c97d860a8009f92.jpg') center/cover no-repeat;
            padding: 80px 20px;
            position: relative;
            overflow: hidden;
        }

        .contact-card-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.7);
            z-index: 1;
        }

        .contact-card-section::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
            z-index: 2;
        }

        .card-wrapper {
            max-width: 1000px;
            width: 100%;
            margin: 0 auto;
            position: relative;
            z-index: 3;
        }

        .card-wrapper h2 {
            text-align: center;
            color: white;
            font-size: 2.2rem;
            font-weight: 300;
            margin-bottom: 30px;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
            animation: fadeInUp 1s ease-out;
        }

        .contact-card {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(30px);
            border-radius: 25px;
            padding: 35px;
            box-shadow:
                0 20px 40px rgba(0, 0, 0, 0.15),
                0 8px 20px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
            animation: slideInUp 1.2s ease-out 0.3s both;
        }

        .contact-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6px;
            background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 50%, #8b5cf6 100%);
        }

        #visaContact {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            align-items: start;
        }

        .field {
            display: flex;
            flex-direction: column;
            position: relative;
            animation: fadeInUp 0.8s ease-out both;
        }

        .field.full {
            grid-column: 1 / -1;
        }

        /* Staggered Animation Delays */
        .field:nth-child(1) { animation-delay: 0.1s; }
        .field:nth-child(2) { animation-delay: 0.2s; }
        .field:nth-child(3) { animation-delay: 0.3s; }
        .field:nth-child(4) { animation-delay: 0.4s; }
        .field:nth-child(5) { animation-delay: 0.5s; }
        .field:nth-child(6) { animation-delay: 0.6s; }
        .field:nth-child(7) { animation-delay: 0.7s; }
        .field:nth-child(8) { animation-delay: 0.8s; }
        .field:nth-child(9) { animation-delay: 0.9s; }
        .field:nth-child(10) { animation-delay: 1s; }
        .field:nth-child(11) { animation-delay: 1.1s; }
        .field:nth-child(12) { animation-delay: 1.2s; }
        .field:nth-child(13) { animation-delay: 1.3s; }

        .field label {
            font-size: 13px;
            font-weight: 700;
            color: #0f172a;
            margin-bottom: 6px;
            display: flex;
            align-items: center;
            gap: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .field label::before {
            content: '';
            width: 4px;
            height: 4px;
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        .input-container {
            position: relative;
            display: flex;
            align-items: center;
            background: #f8fafc;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            overflow: hidden;
            min-height: 42px;
        }

        .input-container:hover {
            border-color: #cbd5e1;
            background: #f1f5f9;
            transform: translateY(-2px);
        }

        .input-container:focus-within {
            border-color: #3b82f6;
            background: white;
            box-shadow: 0 0 0 6px rgba(59, 130, 246, 0.1);
            transform: translateY(-3px);
        }

        .input-icon {
            width: 42px;
            padding: 0;
            color: #64748b;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #e2e8f0;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px 0 0 10px;
            flex-shrink: 0;
        }

        .input-container:focus-within .input-icon {
            color: white;
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            transform: scale(1.1);
        }

        .input-container input,
        .input-container select,
        .input-container textarea {
            flex: 1;
            border: none;
            outline: none;
            padding: 10px 16px;
            font-size: 14px;
            background: transparent;
            color: #0f172a;
            font-family: inherit;
            font-weight: 500;
        }

        .input-container input::placeholder,
        .input-container textarea::placeholder {
            color: #94a3b8;
            font-weight: 400;
        }

        .input-container select {
            cursor: pointer;
            appearance: none;
            background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="%233b82f6" stroke-width="3"><polyline points="6,9 12,15 18,9"></polyline></svg>');
            background-repeat: no-repeat;
            background-position: right 20px center;
            background-size: 18px;
            padding-right: 50px;
        }

        .input-container textarea {
            resize: vertical;
            min-height: 70px;
            font-family: inherit;
        }
        .file-input {
            position: relative;
            cursor: pointer;
            min-height: 60px;
        }

        .file-input input[type="file"] {
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }

        .file-text {
            flex: 1;
            padding: 18px 25px;
            color: #94a3b8;
            font-size: 16px;
            display: flex;
            align-items: center;
            font-weight: 500;
        }

        .file-input:hover .file-text {
            color: #3b82f6;
        }

        .btn-submit {
            grid-column: 1 / -1;
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 12px;
            font-size: 15px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            margin-top: 15px;
            box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4);
            position: relative;
            overflow: hidden;
            animation: fadeInUp 1s ease-out 1.5s both;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn-submit::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.6s;
        }

        .btn-submit:hover::before {
            left: 100%;
        }

        .btn-submit:hover {
            transform: translateY(-5px) scale(1.02);
            box-shadow: 0 20px 50px rgba(59, 130, 246, 0.6);
            background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
        }

        .btn-submit:active {
            transform: translateY(-2px) scale(1.01);
        }

        .error-message {
            color: #ef4444;
            font-size: 13px;
            margin-top: 8px;
            opacity: 0;
            transition: opacity 0.3s ease;
            font-weight: 600;
        }

        .field.error .error-message {
            opacity: 1;
        }

        .field.error .input-container {
            border-color: #ef4444;
            background: #fef2f2;
        }

        small {
            color: #64748b;
            font-size: 13px;
            margin-top: 8px;
            display: block;
            font-weight: 500;
        }

        /* MAP SECTION */
        .map-area {
            padding: 120px 0;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }

        .map-area h2 {
            text-align: center;
            font-size: 3rem;
            font-weight: 300;
            color: #1e40af;
            margin-bottom: 60px;
        }

        .map-frame {
            border-radius: 30px;
            overflow: hidden;
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
            border: 3px solid #e2e8f0;
        }

        /* ANIMATIONS */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(40px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(60px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
                opacity: 1;
            }
            50% {
                transform: scale(1.2);
                opacity: 0.8;
            }
        }

        /* RESPONSIVE DESIGN */
        @media (max-width: 768px) {
            .contact-hero-section {
                min-height: 45vh;
                padding: 40px 0 30px;
            }

            .contact-hero-content {
                grid-template-columns: 1fr;
                gap: 30px;
                text-align: center;
            }

            .contact-hero-text h1 {
                font-size: 1.8rem;
                line-height: 1.4;
            }

            .contact-hero-actions {
                justify-content: center;
            }

            .features-grid-enhanced {
                grid-template-columns: repeat(2, 1fr);
                gap: 20px;
            }

            .contact-card-section {
                padding: 60px 15px;
            }

            .card-wrapper h2 {
                font-size: 1.8rem;
                margin-bottom: 30px;
            }

            .contact-card {
                padding: 30px 25px;
                border-radius: 20px;
            }

            #visaContact {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .input-container {
                min-height: 45px;
            }

            .input-icon {
                width: 45px;
                font-size: 16px;
            }

            .input-container input,
            .input-container select,
            .input-container textarea {
                padding: 12px 16px;
                font-size: 14px;
            }

            .btn-submit {
                padding: 14px 30px;
                font-size: 15px;
            }
        }

        @media (max-width: 640px) {
            .features-grid-enhanced {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .feature-card {
                min-height: 240px;
                padding: 25px 15px;
            }

            .feature-icon {
                width: 60px;
                height: 60px;
                margin-bottom: 15px;
            }

            .feature-icon i {
                font-size: 1.6rem;
            }

            .feature-content h3 {
                font-size: 1.1rem;
                margin-bottom: 10px;
            }

            .feature-content p {
                font-size: 0.85rem;
                margin-bottom: 15px;
            }

            .feature-stats {
                padding: 12px;
            }

            .stat-number {
                font-size: 1.5rem;
            }

            .stat-label {
                font-size: 0.7rem;
            }
        }

        @media (max-width: 480px) {
            .contact-hero-section {
                min-height: 40vh;
                padding: 30px 0 20px;
            }

            .contact-hero-text h1 {
                font-size: 1.4rem;
                line-height: 1.4;
            }

            .card-wrapper h2 {
                font-size: 1.6rem;
                margin-bottom: 25px;
            }

            .contact-card {
                padding: 25px 20px;
            }

            .input-icon {
                width: 40px;
                font-size: 14px;
            }

            .input-container {
                min-height: 40px;
            }

            .input-container input,
            .input-container select,
            .input-container textarea {
                padding: 10px 14px;
                font-size: 13px;
            }

            .btn-submit {
                padding: 12px 25px;
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation - Same as Home Page -->
    <nav-div>
        <nav>
            <img src="images/logo/WING LOGO.png" class="logo">

            <!-- Desktop Menu -->
            <div id="links" class="desktop-menu">
                <ul>
                    <li><a href="index.html">Home</a></li>
                    <li><a href="about.html">About</a></li>
                    <li class="dropdown">
                        <a href="#" onclick="return false;">Services <i class="ri-arrow-down-s-line"></i></a>
                        <div class="dropdown-content">
                            <a href="study.html">
                                <i class="ri-graduation-cap-line"></i>
                                <div>
                                    <strong>Study Abroad</strong>
                                    <span>Student visa & education guidance</span>
                                </div>
                            </a>
                            <a href="migrate.html">
                                <i class="ri-home-heart-line"></i>
                                <div>
                                    <strong>Migrate</strong>
                                    <span>Permanent residency & immigration</span>
                                </div>
                            </a>
                            <a href="work.html">
                                <i class="ri-briefcase-line"></i>
                                <div>
                                    <strong>Work Abroad</strong>
                                    <span>Work visa & job opportunities</span>
                                </div>
                            </a>
                            <a href="visit.html">
                                <i class="ri-plane-line"></i>
                                <div>
                                    <strong>Visit</strong>
                                    <span>Tourist & business visa</span>
                                </div>
                            </a>
                        </div>
                    </li>
                    <li><a href="contact.html">Contact</a></li>
                </ul>

                <!-- Apply Now Button for Desktop -->
                <div class="apply-now-btn">
                    <a href="https://wa.me/************" class="btn-apply">
                        <i class="ri-whatsapp-line"></i>
                        Apply Now
                    </a>
                </div>
            </div>

            <!-- Simple Mobile Hamburger -->
            <button class="simple-hamburger" onclick="openMobileMenu()">
                <span></span>
                <span></span>
                <span></span>
            </button>
        </nav>

        <!-- Professional Mobile Menu -->
        <div class="mobile-menu-overlay"></div>
        <div class="mobile-menu-sidebar">
            <div class="mobile-menu-header">
                <img src="images/logo/WING LOGO.png" class="mobile-menu-logo">
                <button class="mobile-menu-close" onclick="closeMobileMenu()">×</button>
            </div>
            <ul class="mobile-menu-links">
                <li><a href="index.html">Home</a></li>
                <li><a href="about.html">About</a></li>
                <li class="mobile-services-dropdown">
                    <div class="mobile-services-toggle" onclick="toggleServices()">
                        <span>Services</span>
                        <i class="ri-arrow-down-s-line mobile-services-arrow"></i>
                    </div>
                    <div class="mobile-services-links">
                        <a href="study.html">Study Abroad</a>
                        <a href="migrate.html">Migrate</a>
                        <a href="work.html">Work Abroad</a>
                        <a href="visit.html">Visit</a>
                    </div>
                </li>
                <li><a href="contact.html" class="active">Contact</a></li>
            </ul>
        </div>
    </nav-div>

    <!-- HERO SECTION -->
    <section class="contact-hero-section">
        <div class="contact-hero-container">
            <div class="contact-hero-content">
                <div class="contact-hero-text">
                    <div class="contact-hero-badge">
                        <i class="ri-customer-service-2-line"></i>
                        <span>Expert Consultation</span>
                    </div>
                    <h1>Connect with White Wings<br>Your Trusted Global Visa Partners</h1>
                    <div class="contact-hero-description">
                        <p>Transform your international aspirations into reality with White Wings Visa Consultancy. Our expert team brings years of specialized experience and an exceptional 98% success rate to make your visa journey seamless and stress-free.</p>

                        <p>From student visas to permanent residency, work permits to tourist visas — we provide comprehensive, personalized solutions tailored to your unique circumstances and destination requirements.</p>
                    </div>

                    <div class="contact-hero-actions">
                        <a href="tel:+************" class="contact-btn-primary">
                            <i class="ri-phone-line"></i>
                            Call Expert Now
                        </a>
                        <a href="https://wa.me/************" class="contact-btn-secondary">
                            <i class="ri-whatsapp-line"></i>
                            WhatsApp Chat
                        </a>
                    </div>
                </div>

                <div class="contact-hero-visual">
                    <div class="contact-hero-image-container">
                        <img src="images/hero/top-view-green-card-passport.jpg" alt="Professional Visa Consultation - White Wings" class="contact-hero-image">
                        <div class="contact-image-overlay">
                            <div class="contact-overlay-content">
                                <div class="contact-overlay-badge">
                                    <i class="ri-shield-check-line"></i>
                                    <span>Trusted by 1000+ Clients</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- WHY CHOOSE US - Enhanced Professional Design -->
    <section class="features-area-enhanced">
        <div class="features-container">
            <div class="features-header">
                <div class="codex-section-title" style="text-align: center !important; width: 100% !important;">
                    <h6 style="text-align: center !important; display: block !important; margin: 0 auto 15px auto !important; padding: 8px 20px !important; background: rgba(104, 127, 229, 0.1) !important; border-radius: 30px !important; font-size: 14px !important; font-weight: 600 !important; text-transform: uppercase !important; letter-spacing: 1px !important; color: #687FE5 !important; border: 1px solid rgba(104, 127, 229, 0.2) !important;">Why Choose Us</h6>
                    <h2 style="text-align: center !important; margin: 0 auto !important; font-size: 2.8rem !important; font-weight: 700 !important; color: #1e293b !important;">Why Choose Our Visa Services</h2>
                    <p style="max-width: 700px; margin: 15px auto 0; text-align: center !important; font-size: 1.1rem !important; color: #64748b !important; line-height: 1.6 !important;">Experience excellence in visa consultation with our comprehensive range of professional services designed to make your journey seamless.</p>
                </div>
            </div>

            <div class="features-grid-enhanced">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="ri-team-line"></i>
                    </div>
                    <div class="feature-content">
                        <h3>Expert Team</h3>
                        <p>Decades of combined experience ensuring flawless applications with personalized attention to every detail.</p>
                        <div class="feature-stats">
                            <span class="stat-number">10+</span>
                            <span class="stat-label">Years Experience</span>
                        </div>
                    </div>
                    <div class="feature-overlay"></div>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="ri-speed-line"></i>
                    </div>
                    <div class="feature-content">
                        <h3>Fast Processing</h3>
                        <p>Streamlined workflows and efficient processes that cut waiting times dramatically while maintaining quality.</p>
                        <div class="feature-stats">
                            <span class="stat-number">98%</span>
                            <span class="stat-label">Success Rate</span>
                        </div>
                    </div>
                    <div class="feature-overlay"></div>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="ri-price-tag-3-line"></i>
                    </div>
                    <div class="feature-content">
                        <h3>Transparent Pricing</h3>
                        <p>No hidden charges—clear, competitive rates from the start with detailed cost breakdown for every service.</p>
                        <div class="feature-stats">
                            <span class="stat-number">100%</span>
                            <span class="stat-label">Transparent</span>
                        </div>
                    </div>
                    <div class="feature-overlay"></div>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="ri-customer-service-2-line"></i>
                    </div>
                    <div class="feature-content">
                        <h3>24/7 Support</h3>
                        <p>Always on call for advice, updates, or last-minute changes with dedicated support throughout your journey.</p>
                        <div class="feature-stats">
                            <span class="stat-number">24/7</span>
                            <span class="stat-label">Available</span>
                        </div>
                    </div>
                    <div class="feature-overlay"></div>
                </div>
            </div>
        </div>
    </section>

    <!-- MODERN CONTACT FORM SECTION -->
    <section class="contact-card-section">
        <div class="card-wrapper">
            <h2>Start Your Visa Journey Today</h2>
            <div class="contact-card">
                <form id="visaContact" action="https://formsubmit.co/<EMAIL>" method="POST" enctype="multipart/form-data" novalidate>
                    <input type="hidden" name="_subject" value="New Visa Application Request">
                    <input type="hidden" name="_next" value="https://whitewingsvisa.com/thank-you.html">
                    <input type="hidden" name="_captcha" value="false">
                    <div class="field">
                        <label for="name">Full Legal Name</label>
                        <div class="input-container">
                            <span class="input-icon"><i class="ri-user-3-line"></i></span>
                            <input type="text" id="name" name="name" placeholder="Enter your complete legal name as per passport" required />
                        </div>
                        <div class="error-message"></div>
                    </div>

                    <div class="field">
                        <label for="email">Email Address</label>
                        <div class="input-container">
                            <span class="input-icon"><i class="ri-mail-line"></i></span>
                            <input type="email" id="email" name="email" placeholder="<EMAIL>" required />
                        </div>
                        <div class="error-message"></div>
                    </div>

                    <div class="field">
                        <label for="phone">Contact Number</label>
                        <div class="input-container">
                            <span class="input-icon"><i class="ri-smartphone-line"></i></span>
                            <input type="tel" id="phone" name="phone" placeholder="+91 XXXXX XXXXX" required />
                        </div>
                        <div class="error-message"></div>
                    </div>

                    <div class="field">
                        <label for="country">Current Residence</label>
                        <div class="input-container">
                            <span class="input-icon"><i class="ri-map-pin-line"></i></span>
                            <input type="text" id="country" name="country" placeholder="Country where you currently reside" required />
                        </div>
                        <div class="error-message"></div>
                    </div>

                    <div class="field">
                        <label for="visaType">Visa Category</label>
                        <div class="input-container">
                            <span class="input-icon"><i class="ri-passport-line"></i></span>
                            <select id="visaType" name="visaType" required>
                                <option value="">Choose your visa category</option>
                                <option>Student Visa (Study Abroad)</option>
                                <option>Work Visa (Employment)</option>
                                <option>Tourist/Visitor Visa</option>
                                <option>Business Visa</option>
                                <option>Permanent Residency</option>
                                <option>Family/Spouse Visa</option>
                                <option>Transit Visa</option>
                                <option>Other (Please specify in notes)</option>
                            </select>
                        </div>
                        <div class="error-message"></div>
                    </div>

                    <div class="field">
                        <label for="destination">Destination Country</label>
                        <div class="input-container">
                            <span class="input-icon"><i class="ri-global-line"></i></span>
                            <select id="destination" name="destination" required>
                                <option value="">Select destination country</option>
                                <option>United States (USA)</option>
                                <option>Canada</option>
                                <option>United Kingdom (UK)</option>
                                <option>Australia</option>
                                <option>Germany</option>
                                <option>France</option>
                                <option>Netherlands</option>
                                <option>New Zealand</option>
                                <option>Singapore</option>
                                <option>Dubai (UAE)</option>
                                <option>Other (Please specify in notes)</option>
                            </select>
                        </div>
                        <div class="error-message"></div>
                    </div>

                    <div class="field">
                        <label for="travelDate">Intended Travel Date</label>
                        <div class="input-container">
                            <span class="input-icon"><i class="ri-calendar-event-line"></i></span>
                            <input type="date" id="travelDate" name="travelDate" required />
                        </div>
                        <div class="error-message"></div>
                    </div>

                    <div class="field">
                        <label for="passportStatus">Passport Status</label>
                        <div class="input-container">
                            <span class="input-icon"><i class="ri-book-open-line"></i></span>
                            <select id="passportStatus" name="passportStatus" required>
                                <option value="">Select passport status</option>
                                <option>Have valid passport (>6 months validity)</option>
                                <option>Have passport (expires within 6 months)</option>
                                <option>Passport renewal in progress</option>
                                <option>Need to apply for new passport</option>
                            </select>
                        </div>
                        <div class="error-message"></div>
                    </div>

                    <div class="field">
                        <label for="travelers">Number of Applicants</label>
                        <div class="input-container">
                            <span class="input-icon"><i class="ri-team-line"></i></span>
                            <select id="travelers" name="travelers" required>
                                <option value="">Select number of applicants</option>
                                <option>1 (Individual)</option>
                                <option>2 (Couple/Family)</option>
                                <option>3-4 (Small Family)</option>
                                <option>5+ (Large Group)</option>
                            </select>
                        </div>
                        <div class="error-message"></div>
                    </div>

                    <div class="field">
                        <label for="contactPref">Preferred Communication</label>
                        <div class="input-container">
                            <span class="input-icon"><i class="ri-message-3-line"></i></span>
                            <select id="contactPref" name="contactPref" required>
                                <option value="">Choose preferred method</option>
                                <option>WhatsApp (Fastest Response)</option>
                                <option>Phone Call</option>
                                <option>Email</option>
                                <option>Video Consultation</option>
                            </select>
                        </div>
                        <div class="error-message"></div>
                    </div>

                    <div class="field">
                        <label for="urgency">Application Urgency</label>
                        <div class="input-container">
                            <span class="input-icon"><i class="ri-time-line"></i></span>
                            <select id="urgency" name="urgency" required>
                                <option value="">Select timeline</option>
                                <option>Urgent (Within 1-2 weeks)</option>
                                <option>Standard (1-2 months)</option>
                                <option>Flexible (3+ months)</option>
                                <option>Just exploring options</option>
                            </select>
                        </div>
                        <div class="error-message"></div>
                    </div>

                    <div class="field">
                        <label for="experience">Previous Visa Experience</label>
                        <div class="input-container">
                            <span class="input-icon"><i class="ri-history-line"></i></span>
                            <select id="experience" name="experience" required>
                                <option value="">Select your experience</option>
                                <option>First-time applicant</option>
                                <option>Previously approved visa</option>
                                <option>Previous visa rejection</option>
                                <option>Multiple visa experience</option>
                            </select>
                        </div>
                        <div class="error-message"></div>
                    </div>

                    <div class="field full">
                        <label for="docs">Upload Documents (Optional)</label>
                        <div class="input-container file-input">
                            <span class="input-icon"><i class="ri-upload-cloud-line"></i></span>
                            <input type="file" id="docs" name="docs" accept=".pdf,.jpg,.png,.jpeg" multiple />
                            <span class="file-text">Upload passport copy, photos, or other documents</span>
                        </div>
                        <small>Accepted formats: PDF, JPG, PNG (Max 10MB per file)</small>
                        <div class="error-message"></div>
                    </div>

                    <div class="field full">
                        <label for="notes">Additional Information</label>
                        <div class="input-container">
                            <span class="input-icon"><i class="ri-chat-3-line"></i></span>
                            <textarea id="notes" name="notes" rows="4" placeholder="Share any specific requirements, concerns, or questions about your visa application..."></textarea>
                        </div>
                        <div class="error-message"></div>
                    </div>

                    <button type="submit" class="btn-submit">
                        <i class="ri-rocket-line"></i>
                        Start My Visa Journey
                    </button>
                </form>

                <!-- Success Message (Hidden by default) -->
                <div class="success-card" style="display: none;">
                    <div class="success-icon">
                        <i class="ri-check-double-line"></i>
                    </div>
                    <h3>Application Received Successfully!</h3>
                    <p>Thank you for choosing White Wings Visa Consultancy. Our expert team will review your application and contact you within 2-4 hours with a personalized consultation plan.</p>
                    <button class="btn-new-request" onclick="showForm()">Submit Another Application</button>
                </div>
            </div>
        </div>
    </section>

    <!-- MAP SECTION -->
    <section class="map-area">
        <div class="wrapper">
            <h2>Visit Our Pune Office</h2>
            <div class="map-frame">
                <iframe
                    src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3783.2674515309!2d73.8567437!3d18.5204303!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3bc2c0d0d0d0d0d0%3A0x0!2sPune%2C%20Maharashtra!5e0!3m2!1sen!2sin!4v1234567890123!5m2!1sen!2sin"
                    width="100%" height="400" style="border:0;" allowfullscreen="" loading="lazy">
                </iframe>
            </div>
        </div>
    </section>

    <!-- Footer - Same as Home Page -->
    <footer class="footer-section">
        <!-- Apply for Visa CTA Section -->
        <div class="apply-visa-cta">
            <div class="cta-container">
                <h6>APPLY FOR A VISA</h6>
                <h2>You Focus on Flying, We'll Handle the Paperwork!</h2>
                <p>Planning your international journey from India? Let White Wings Visa Consultancy in Pune handle all the complex visa processes. From USA to Canada, UK to Australia - we make your global travel dreams come true with expert guidance and guaranteed success.</p>
                <a href="https://wa.me/************" class="apply-btn">APPLY NOW</a>
            </div>
        </div>

        <!-- Main Footer -->
        <div class="main-footer">
            <div class="footer-container">
                <div class="footer-content">
                    <!-- Left Column - Logo and Social -->
                    <div class="footer-left">
                        <img src="images/logo/WING LOGO.png" alt="White Wings Logo" class="footer-logo">
                        <p class="footer-tagline">Hassle-free travel shouldn't just be a dream.</p>
                        <p class="footer-slogan">White Wings - Visa Simplified.</p>
                        <div class="social-links">
                            <a href="https://wa.me/************"><i class="ri-whatsapp-fill"></i></a>
                            <a href="mailto:<EMAIL>"><i class="ri-mail-fill"></i></a>
                            <a href="#"><i class="ri-instagram-fill"></i></a>
                            <a href="#"><i class="ri-facebook-fill"></i></a>
                        </div>
                    </div>

                    <!-- Middle Column - Quick Links -->
                    <div class="footer-middle">
                        <h4>Quick Links</h4>
                        <ul>
                            <li><a href="about.html">About</a></li>
                            <li><a href="services.html">Services</a></li>
                            <li><a href="contact.html">Contact</a></li>
                            <li><a href="contact.html">Apply Now</a></li>
                            <li><a href="tel:+************">Call Us</a></li>
                        </ul>
                    </div>

                    <!-- Right Column - Contact Info -->
                    <div class="footer-right">
                        <h4>Get In Touch</h4>
                        <div class="contact-details">
                            <p>📞 +91 9130448831</p>
                            <p><strong>✉ <EMAIL></strong></p>
                            <p>📍 Pune, Maharashtra, India</p>
                        </div>
                    </div>
                </div>

                <!-- Copyright -->
                <div class="footer-bottom">
                    <div class="footer-bottom-content">
                        <p>Copyright © 2025 White Wings. All Rights Reserved.</p>
                        <p>Powered by White Wings</p>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- GSAP -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>

    <!-- Lenis Smooth Scroll -->
    <script src="https://unpkg.com/lenis@1.3.4/dist/lenis.min.js"></script>

    <!-- Scripts -->
    <script src="universal-mobile-fix.js"></script>
    <script src="universal-lenis.js"></script>
    <script src="script.js"></script>

    <script>
        function openMobileMenu() {
            const overlay = document.querySelector('.mobile-menu-overlay');
            const sidebar = document.querySelector('.mobile-menu-sidebar');
            if (overlay && sidebar) {
                overlay.classList.add('show');
                sidebar.classList.add('show');
                document.body.style.overflow = 'hidden';
            }
        }

        function closeMobileMenu() {
            const overlay = document.querySelector('.mobile-menu-overlay');
            const sidebar = document.querySelector('.mobile-menu-sidebar');
            const servicesDropdown = document.querySelector('.mobile-services-dropdown');
            if (overlay && sidebar) {
                overlay.classList.remove('show');
                sidebar.classList.remove('show');
                document.body.style.overflow = 'auto';
                if (servicesDropdown) {
                    servicesDropdown.classList.remove('active');
                }
            }
        }

        function toggleServices() {
            const servicesDropdown = document.querySelector('.mobile-services-dropdown');
            if (servicesDropdown) {
                servicesDropdown.classList.toggle('active');
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            const overlay = document.querySelector('.mobile-menu-overlay');
            if (overlay) {
                overlay.addEventListener('click', function(e) {
                    if (e.target === overlay) closeMobileMenu();
                });
            }

            const menuLinks = document.querySelectorAll('.mobile-menu-links a:not(.mobile-services-toggle)');
            menuLinks.forEach(link => {
                link.addEventListener('click', function() {
                    closeMobileMenu();
                });
            });

            const serviceLinks = document.querySelectorAll('.mobile-services-links a');
            serviceLinks.forEach(link => {
                link.addEventListener('click', function() {
                    closeMobileMenu();
                });
            });
        });
    </script>

</body>
</html>