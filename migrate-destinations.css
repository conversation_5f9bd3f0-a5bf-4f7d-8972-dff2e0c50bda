/* Migrate Destinations Styles */
.migrate-opportunity-section {
    padding: 80px 0;
    background: #fff;
    position: relative;
    z-index: 3;
}

.migrate-opportunity-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.section-header {
    text-align: center;
    margin-bottom: 50px;
}

.section-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: #f0f9ff;
    color: #3b82f6;
    padding: 8px 16px;
    border-radius: 50px;
    margin-bottom: 20px;
    font-weight: 600;
}

.section-badge i {
    font-size: 1.2rem;
}

.section-header h2 {
    font-size: 2.5rem;
    color: #222;
    margin-bottom: 15px;
}

.section-header p {
    color: #64748b;
    font-size: 1.1rem;
    max-width: 700px;
    margin: 0 auto;
}

.destinations-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
}

.destination-card {
    background: white;
    border-radius: 15px;
    padding: 0;
    box-shadow: 0 10px 30px rgba(0, 0, 128, 0.05);
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
    position: relative;
    border: 1px solid #f1f5f9;
}

.destination-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 128, 0.1);
    border-color: #e2e8f0;
}

.destination-card::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, #000080, #3b82f6);
    z-index: 1;
}

.destination-flag {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid white;
    box-shadow: 0 5px 15px rgba(0, 0, 128, 0.1);
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 2;
    background: white;
}

.destination-flag img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.destination-header {
    height: 120px;
    background: linear-gradient(135deg, #f8fafc 0%, #f0f9ff 100%);
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.destination-icon {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(5px);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 5px 15px rgba(0, 0, 128, 0.05);
    transition: all 0.3s ease;
}

.destination-icon i {
    font-size: 24px;
    color: #000080;
}

.destination-card:hover .destination-icon {
    transform: scale(1.1);
    background: white;
}

.destination-header::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 30px;
    background: linear-gradient(to top, white, transparent);
}

.destination-content {
    padding: 25px;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
}

.destination-card h3 {
    font-size: 1.5rem;
    color: #000080;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.destination-features {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 15px;
}

.feature-tag {
    background: #f0f9ff;
    color: #3b82f6;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.destination-card:hover .feature-tag {
    background: #e0f2fe;
}

.destination-card p {
    color: #64748b;
    margin-bottom: 20px;
    flex-grow: 1;
    line-height: 1.6;
}

.destination-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 24px;
    background: #A4B1BE;
    color: #000080;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    text-align: center;
    border: 1px solid #e2e8f0;
}

.destination-btn i {
    transition: all 0.3s ease;
}

.destination-btn:hover {
    background: #3131ff;
    border-color: #ffffff;
}

.destination-btn:hover i {
    transform: translateX(5px);
}

/* Responsive styles */
@media (max-width: 992px) {
    .destinations-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .destinations-grid {
        grid-template-columns: 1fr;
    }
    
    .section-header h2 {
        font-size: 2rem;
    }
}