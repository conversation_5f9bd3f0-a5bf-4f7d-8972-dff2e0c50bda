/*
 * MIGRATE PAGE SPECIFIC STYLES
 * White Wings Visa - Migrate Page Only CSS
 * Contains all styles specific to migrate.html
 */

/* Import all required styles for migrate page */
@import url('style.css');
@import url('professional-mobile-menu.css');
@import url('style-fix.css');
@import url('pages.css');
@import url('migrate-styles.css');
@import url('migrate-destinations.css');
@import url('migrate-process.css');
@import url('migrate-benefits.css');
@import url('migrate-sections.css');
@import url('master-mobile-navbar.css');
@import url('apply-now-button.css');
@import url('white-wings-colors.css');
@import url('text-visibility-fix.css');
@import url('white-wings-design-system.css');
@import url('button-icon-overrides.css');

/* Migrate page specific overrides and enhancements */
.migrate-hero-section {
    /* Migrate page hero specific styles */
}

.migrate-destinations-section {
    /* Migrate page destinations section specific styles */
}

.migrate-process-section {
    /* Migrate page process section specific styles */
}

.migrate-benefits-section {
    /* Migrate page benefits section specific styles */
}

.migrate-requirements-section {
    /* Migrate page requirements section specific styles */
}

/* Migrate page specific responsive styles */
@media (max-width: 768px) {
    .migrate-hero-section {
        /* Mobile specific migrate hero styles */
    }
    
    .migrate-destinations-section {
        /* Mobile specific migrate destinations styles */
    }
}

/* Migrate page specific animations */
@keyframes migrate-zoom-in {
    from { opacity: 0; transform: scale(0.9); }
    to { opacity: 1; transform: scale(1); }
}

.migrate-animate {
    animation: migrate-zoom-in 0.7s ease-out;
}
