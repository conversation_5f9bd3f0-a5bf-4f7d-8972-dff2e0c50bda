/* Additional styles for modern mission & vision section */
.modern-mv-section {
    padding: 100px 0;
    background: #f8fafc;
}

.mv-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.section-header {
    text-align: center;
    margin-bottom: 60px;
}

.section-header h2 {
    font-size: 2.5rem;
    color: #1e293b;
    margin-bottom: 15px;
}

.section-header p {
    color: #64748b;
    max-width: 700px;
    margin: 0 auto;
    line-height: 1.7;
}

.mv-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
}

.mv-card {
    background: white;
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
}

.mv-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.card-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
}

.card-icon {
    width: 50px;
    height: 50px;
    background: #f0f9ff;
    color: #3b82f6;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.card-badge {
    background: #f0f9ff;
    color: #3b82f6;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.mv-card h3 {
    color: #1e293b;
    font-size: 1.5rem;
    margin-bottom: 15px;
}

.mv-card p {
    color: #64748b;
    margin-bottom: 20px;
    line-height: 1.7;
}

.card-features {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 10px;
}

.feature-item i {
    color: #10b981;
    font-size: 1.2rem;
}

.feature-item span {
    color: #1e293b;
    font-weight: 500;
}

.modern-mission .card-icon {
    background: #f0f9ff;
    color: #3b82f6;
}

.modern-mission .card-badge {
    background: #f0f9ff;
    color: #3b82f6;
}

.modern-vision .card-icon {
    background: #ecfdf5;
    color: #10b981;
}

.modern-vision .card-badge {
    background: #ecfdf5;
    color: #10b981;
}

/* Core Values Section */
.modern-values-section {
    padding: 100px 0;
    background: white;
}

.values-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.value-card {
    background: #C5D5E6;
    border-radius: 20px;
    padding: 30px;
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid #e2e8f0;
    position: relative;
    overflow: hidden;
}

.value-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
    border-color: #3b82f6;
}

.value-icon {
    width: 70px;
    height: 70px;
    background: #f0f9ff;
    color: #3b82f6;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    margin: 0 auto 20px;
}

.value-card h4 {
    color: #1e293b;
    font-size: 1.3rem;
    margin-bottom: 15px;
}

.value-card p {
    color: #64748b;
    margin-bottom: 20px;
    line-height: 1.7;
}

.value-highlight {
    background: #f0f9ff;
    color: #3b82f6;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    display: inline-block;
}

/* Timeline Section */
.modern-timeline-section {
    padding: 100px 0;
    background: #f8fafc;
}

.timeline-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 0 20px;
}

.modern-timeline {
    position: relative;
    padding-left: 50px;
}

.modern-timeline::before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 20px;
    width: 2px;
    background: #e2e8f0;
}

.timeline-item {
    position: relative;
    margin-bottom: 50px;
}

.timeline-item:last-child {
    margin-bottom: 0;
}

.timeline-marker {
    position: absolute;
    top: 0;
    left: -50px;
    width: 40px;
    height: 40px;
    background: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 0 0 4px white, 0 0 0 5px #e2e8f0;
    z-index: 1;
}

.marker-icon {
    width: 30px;
    height: 30px;
    background: #f0f9ff;
    color: #3b82f6;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
}

.timeline-content {
    background: white;
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
}

.timeline-year {
    display: inline-block;
    background: #C5D5E6;
    color: #111;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 15px;
}

.timeline-content h4 {
    color: #1e293b;
    font-size: 1.3rem;
    margin-bottom: 15px;
}

.timeline-content p {
    color: #64748b;
    margin-bottom: 15px;
    line-height: 1.7;
}

.timeline-stats {
    display: inline-block;
    background: #f8fafc;
    color: #64748b;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
}

/* Team Section */
.modern-team-section {
    padding: 100px 0;
    background: white;
}

.team-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.team-member {
    background: #f8fafc;
    border-radius: 20px;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid #e2e8f0;
}

.team-member:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
    border-color: #3b82f6;
}

.member-image {
    position: relative;
    height: 300px;
    overflow: hidden;
}

.member-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s ease;
}

.team-member:hover .member-image img {
    transform: scale(1.05);
}

.member-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
}

.team-member:hover .member-overlay {
    opacity: 1;
}

.social-links {
    display: flex;
    gap: 15px;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: white;
    color: #3b82f6;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: #3b82f6;
    color: white;
    transform: translateY(-3px);
}

.member-info {
    padding: 30px;
}

.member-info h4 {
    color: #1e293b;
    font-size: 1.3rem;
    margin-bottom: 5px;
}

.member-role {
    color: #666666;
    font-weight: 600;
    margin-bottom: 15px;
}

.member-description {
    color: #64748b;
    margin-bottom: 15px;
    line-height: 1.7;
}

.member-expertise {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.expertise-tag {
    background: #f0f9ff;
    color: #666666;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

/* Achievements Section */
.enhanced-achievements-section {
    padding: 100px 0;
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
    color: white;
    position: relative;
    overflow: hidden;
}

.achievements-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.achievements-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    z-index: 2;
}

.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 3;
}

.floating-element {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
}

.element-1 {
    width: 300px;
    height: 300px;
    top: -100px;
    left: -100px;
}

.element-2 {
    width: 200px;
    height: 200px;
    bottom: -50px;
    right: 10%;
}

.element-3 {
    width: 150px;
    height: 150px;
    top: 30%;
    right: -50px;
}

.element-4 {
    width: 100px;
    height: 100px;
    bottom: 20%;
    left: 10%;
}

.achievements-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 5;
}

.achievements-header {
    text-align: center;
    margin-bottom: 60px;
}

.header-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.1);
    padding: 8px 16px;
    border-radius: 50px;
    margin-bottom: 20px;
}

.header-badge i {
    font-size: 1.2rem;
}

.achievements-header h2 {
    font-size: 2.5rem;
    margin-bottom: 15px;
}

.achievements-header p {
    max-width: 700px;
    margin: 0 auto;
    opacity: 0.9;
    line-height: 1.7;
}

.achievements-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.achievement-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 30px;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.achievement-card:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

.card-glow {
    position: absolute;
    width: 150px;
    height: 150px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    filter: blur(40px);
    top: -50px;
    right: -50px;
    z-index: 1;
}

.achievement-icon-wrapper {
    position: relative;
    margin-bottom: 20px;
}

.achievement-icon {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    position: relative;
    z-index: 2;
}

.icon-pulse {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 0.7;
    }
    70% {
        transform: scale(1.5);
        opacity: 0;
    }
    100% {
        transform: scale(1);
        opacity: 0;
    }
}

.achievement-content {
    position: relative;
    z-index: 2;
}

.achievement-number {
    display: flex;
    align-items: baseline;
    margin-bottom: 10px;
}

.count {
    font-size: 3rem;
    font-weight: 700;
}

.number-suffix {
    font-size: 1.5rem;
    font-weight: 700;
    margin-left: 5px;
}

.achievement-card h3 {
    font-size: 1.3rem;
    margin-bottom: 10px;
}

.achievement-card p {
    opacity: 0.9;
    margin-bottom: 20px;
    line-height: 1.7;
}

.achievement-progress {
    height: 6px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: white;
    border-radius: 3px;
    width: 0;
    transition: width 1.5s ease-in-out;
}

.featured-card {
    position: relative;
}

.featured-ribbon {
    position: absolute;
    top: 20px;
    right: -30px;
    background: #f59e0b;
    color: white;
    padding: 5px 30px;
    font-size: 0.8rem;
    font-weight: 600;
    transform: rotate(45deg);
    z-index: 3;
}

.primary-card .achievement-icon {
    background: rgba(59, 130, 246, 0.3);
}

.featured-card .achievement-icon {
    background: rgba(245, 158, 11, 0.3);
}

.secondary-card .achievement-icon {
    background: rgba(16, 185, 129, 0.3);
}

.accent-card .achievement-icon {
    background: rgba(236, 72, 153, 0.3);
}

.achievements-footer {
    margin-top: 60px;
    text-align: center;
}

.footer-stats {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
}

.footer-stat {
    display: flex;
    align-items: center;
    gap: 10px;
    background: rgba(255, 255, 255, 0.1);
    padding: 10px 20px;
    border-radius: 50px;
}

.footer-stat i {
    font-size: 1.2rem;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .mv-grid {
        grid-template-columns: 1fr;
    }
    
    .card-features {
        grid-template-columns: 1fr;
    }
    
    .modern-timeline {
        padding-left: 30px;
    }
    
    .timeline-marker {
        left: -30px;
    }
    
    .team-grid {
        grid-template-columns: 1fr;
    }
    
    .achievements-grid {
        grid-template-columns: 1fr;
    }
    
    .footer-stats {
        flex-direction: column;
        align-items: center;
    }
}