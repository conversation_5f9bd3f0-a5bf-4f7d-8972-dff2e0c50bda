/*
 * WORK PAGE SPECIFIC STYLES
 * White Wings Visa - Work Page Only CSS
 * Contains all styles specific to work.html
 */

/* Import all required styles for work page */
@import url('style.css');
@import url('professional-mobile-menu.css');
@import url('style-fix.css');
@import url('pages.css');
@import url('work-styles.css');
@import url('work-destinations.css');
@import url('work-process.css');
@import url('work-cta-fixed.css');
@import url('simple-mobile-navbar.css');
@import url('apply-now-button.css');
@import url('white-wings-colors.css');
@import url('text-visibility-fix.css');
@import url('white-wings-design-system.css');
@import url('button-icon-overrides.css');

/* Work page specific overrides and enhancements */
.work-hero-section {
    /* Work page hero specific styles */
}

.work-destinations-section {
    /* Work page destinations section specific styles */
}

.work-process-section {
    /* Work page process section specific styles */
}

.work-benefits-section {
    /* Work page benefits section specific styles */
}

.work-cta-section {
    /* Work page CTA section specific styles */
}

/* Work page specific responsive styles */
@media (max-width: 768px) {
    .work-hero-section {
        /* Mobile specific work hero styles */
    }
    
    .work-destinations-section {
        /* Mobile specific work destinations styles */
    }
}

/* Work page specific animations */
@keyframes work-slide-right {
    from { opacity: 0; transform: translateX(-40px); }
    to { opacity: 1; transform: translateX(0); }
}

.work-animate {
    animation: work-slide-right 0.9s ease-out;
}
