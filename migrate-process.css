/* Migration Process Styles */
.migration-process-section {
    padding: 100px 0;
    background: linear-gradient(135deg, #f8fafc 0%, #f0f9ff 100%);
    position: relative;
    z-index: 3;
}

.migration-process-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.process-timeline {
    position: relative;
    margin: 80px 0 60px;
    padding: 0 20px;
}

.timeline-track {
    position: absolute;
    top: 40px;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, #000080, #3b82f6);
    z-index: 1;
    border-radius: 4px;
}

.process-step {
    position: relative;
    z-index: 2;
    display: flex;
    margin-bottom: 60px;
}

.step-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: #000080;
    opacity: 0.2;
    position: absolute;
    top: -20px;
    left: 0;
    line-height: 1;
}

.step-content {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 128, 0.05);
    margin-left: 60px;
    flex: 1;
    position: relative;
    border-top: 4px solid #000080;
    transition: all 0.3s ease;
}

.step-content:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 128, 0.1);
}

.step-icon {
    width: 60px;
    height: 60px;
    background: #f0f9ff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: -30px;
    left: 30px;
    box-shadow: 0 5px 15px rgba(0, 0, 128, 0.1);
    border: 4px solid white;
}

.step-icon i {
    font-size: 24px;
    color: #000080;
}

.process-step h3 {
    font-size: 1.4rem;
    color: #000080;
    margin: 20px 0 15px;
}

.process-step p {
    font-size: 1rem;
    color: #64748b;
    margin-bottom: 20px;
    line-height: 1.6;
}

.step-features {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.step-feature {
    color: white;
        background:#A5B3C0;
    color: #222;
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.process-cta {
    text-align: center;
    margin-top: 40px;
}

.process-btn {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    padding: 15px 30px;
    background: #A5B3C0;
    color: white;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    box-shadow: 0 10px 30px rgba(0, 0, 128, 0.2);
}

.process-btn i {
    transition: all 0.3s ease;
}

.process-btn:hover {
    background: #3b82f6;
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 128, 0.3);
}

.process-btn:hover i {
    transform: translateX(5px);
}

/* Responsive styles */
@media (max-width: 992px) {
    .timeline-track {
        display: none;
    }
    
    .process-step {
        margin-bottom: 80px;
    }
    
    .step-content {
        margin-left: 0;
        padding-top: 40px;
    }
    
    .step-number {
        font-size: 2rem;
        top: -15px;
        left: 30px;
    }
}

@media (max-width: 768px) {
    .migration-process-section {
        padding: 60px 0;
    }
    
    .process-timeline {
        margin: 60px 0 40px;
    }
    
    .step-content {
        padding: 40px 20px 25px;
    }
    
    .step-icon {
        width: 50px;
        height: 50px;
        top: -25px;
        left: 20px;
    }
    
    .step-icon i {
        font-size: 20px;
    }
    
    .process-step h3 {
        font-size: 1.2rem;
        margin: 15px 0 10px;
    }
}