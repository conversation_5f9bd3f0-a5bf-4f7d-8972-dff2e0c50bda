/* ENHANCED UNIVERSITY PLACEMENTS SECTION WITH GRADUATION IMAGE */
.university-placements-section {
    padding: 80px 0;
    background: url('images/hero/group-diverse-grads-throwing-caps-up-sky.jpg') center/cover no-repeat;
    position: relative;
    overflow: hidden;
}

.university-placements-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.75);
    z-index: 1;
}

.placements-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 2;
}

.placements-header {
    text-align: center;
    margin-bottom: 50px;
    position: relative;
    z-index: 2;
}

.placements-header .section-badge {
    display: inline-flex;
    align-items: center;
    gap: 12px;
    background: rgba(59, 130, 246, 0.15);
    backdrop-filter: blur(20px);
    color: #60a5fa;
    padding: 12px 24px;
    border-radius: 50px;
    margin-bottom: 30px;
    font-weight: 700;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 1px;
    border: 1px solid rgba(59, 130, 246, 0.2);
    animation: fadeInUp 0.8s ease-out;
}

.placements-header .section-badge i {
    font-size: 1.4rem;
    color: #3b82f6;
}

.placements-header h2 {
    font-size: 3rem;
    font-weight: 300;
    color: white;
    margin-bottom: 20px;
    line-height: 1.1;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
    animation: fadeInUp 0.8s ease-out 0.2s both;
}

.placements-header p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 1.3rem;
    max-width: 800px;
    margin: 0 auto;
    line-height: 1.6;
    animation: fadeInUp 0.8s ease-out 0.4s both;
}

.universities-by-country {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 25px;
    position: relative;
    z-index: 2;
}

.country-universities {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 30px;
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.2),
        0 8px 20px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    animation: slideInUp 0.8s ease-out both;
}

.country-universities::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 50%, #8b5cf6 100%);
}

.country-universities:hover {
    transform: translateY(-15px) scale(1.02);
    box-shadow:
        0 35px 70px rgba(0, 0, 0, 0.25),
        0 15px 35px rgba(0, 0, 0, 0.15);
}

.country-universities h3 {
    color: #1e40af;
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 3px solid #f1f5f9;
    position: relative;
    display: flex;
    align-items: center;
    gap: 20px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.country-universities h3::after {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 0;
    width: 80px;
    height: 3px;
    background: linear-gradient(90deg, #3b82f6, #1d4ed8);
    border-radius: 2px;
}

.country-flag {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid #e2e8f0;
    display: inline-block;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.country-flag:hover {
    transform: scale(1.1);
    border-color: #3b82f6;
}

.country-flag img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.university-logos {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
}

.university-logo {
    display: flex;
    align-items: center;
    gap: 18px;
    padding: 20px;
    border-radius: 18px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: 2px solid #e2e8f0;
    position: relative;
    overflow: hidden;
}

.university-logo::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
    transition: left 0.6s;
}

.university-logo:hover::before {
    left: 100%;
}

.university-logo:hover {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 15px 35px rgba(59, 130, 246, 0.15);
    border-color: #3b82f6;
}

.university-logo img {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    object-fit: contain;
    border: 3px solid #e2e8f0;
    background: white;
    padding: 8px;
    transition: all 0.4s ease;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.university-logo:hover img {
    border-color: #3b82f6;
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 10px 25px rgba(59, 130, 246, 0.2);
}

.university-logo span {
    font-weight: 700;
    color: #1e293b;
    font-size: 1rem;
    transition: all 0.3s ease;
    line-height: 1.4;
}

.university-logo:hover span {
    color: #1e40af;
    transform: translateX(5px);
}

/* ANIMATIONS */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(40px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(60px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* STAGGERED ANIMATIONS */
.country-universities:nth-child(1) { animation-delay: 0.1s; }
.country-universities:nth-child(2) { animation-delay: 0.2s; }
.country-universities:nth-child(3) { animation-delay: 0.3s; }
.country-universities:nth-child(4) { animation-delay: 0.4s; }

/* RESPONSIVE DESIGN */
@media (max-width: 1200px) {
    .placements-container {
        max-width: 1000px;
    }

    .placements-header h2 {
        font-size: 3rem;
    }
}

@media (max-width: 992px) {
    .university-placements-section {
        padding: 60px 0;
    }

    .universities-by-country {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .placements-header {
        margin-bottom: 40px;
    }

    .placements-header h2 {
        font-size: 2.5rem;
    }

    .country-universities {
        padding: 25px;
    }
}

@media (max-width: 768px) {
    .university-placements-section {
        padding: 50px 0;
    }

    .placements-header h2 {
        font-size: 2rem;
    }

    .placements-header p {
        font-size: 1.1rem;
    }

    .country-universities h3 {
        font-size: 1.4rem;
        gap: 15px;
    }

    .country-flag {
        width: 35px;
        height: 35px;
    }

    .university-logos {
        gap: 15px;
    }

    .university-logo {
        padding: 15px;
        gap: 15px;
    }

    .university-logo img {
        width: 50px;
        height: 50px;
    }

    .university-logo span {
        font-size: 0.9rem;
    }
}

@media (max-width: 576px) {
    .placements-container {
        padding: 0 15px;
    }

    .placements-header .section-badge {
        padding: 10px 20px;
        font-size: 12px;
    }

    .placements-header h2 {
        font-size: 1.8rem;
    }

    .placements-header p {
        font-size: 1rem;
    }

    .country-universities {
        padding: 25px 20px;
        border-radius: 20px;
    }

    .country-universities h3 {
        font-size: 1.2rem;
        margin-bottom: 25px;
        padding-bottom: 15px;
    }

    .country-flag {
        width: 30px;
        height: 30px;
    }

    .university-logos {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .university-logo {
        padding: 12px;
        gap: 12px;
        border-radius: 15px;
    }

    .university-logo img {
        width: 45px;
        height: 45px;
    }

    .university-logo span {
        font-size: 0.85rem;
    }
}