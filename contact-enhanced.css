/* ENHANCED CONTACT PAGE STYLES */

/* Success Card Styling */
.success-card {
    text-align: center;
    padding: 60px 40px;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    border-radius: 30px;
    border: 3px solid #0ea5e9;
    position: relative;
    overflow: hidden;
}

.success-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: linear-gradient(90deg, #0ea5e9 0%, #0284c7 50%, #0369a1 100%);
}

.codex-contact-header {
    text-align: center;
    margin-bottom: 60px;
    position: relative;
}

/* Badge styling moved to badge-fix.css */

.codex-section-title h2 {
    font-size: 36px;
    color: #000080;
    margin-bottom: 20px;
    position: relative;
    padding-bottom: 15px;
    display: inline-block;
}

.codex-section-title h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: linear-gradient(90deg, #000080, #1e40af);
    border-radius: 3px;
}

.codex-contact-content {
    display: flex;
    gap: 40px;
    align-items: stretch;
}

.codex-contact-info {
    flex: 1;
    background: white;
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 15px 40px rgba(0, 0, 128, 0.1);
    display: flex;
    flex-direction: column;
    justify-content: center;
    border: 1px solid rgba(0, 0, 128, 0.05);
    position: relative;
    overflow: hidden;
}

.codex-contact-info::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, #000080, #1e40af);
}

.codex-contact-info-item {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    margin-bottom: 30px;
    transition: all 0.3s ease;
    padding: 15px;
    border-radius: 10px;
}

.codex-contact-info-item:last-child {
    margin-bottom: 0;
}

.codex-contact-info-item:hover {
    background: rgba(0, 0, 128, 0.03);
    transform: translateX(5px);
}

.codex-contact-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #000080, #1e40af);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    box-shadow: 0 8px 20px rgba(0, 0, 128, 0.2);
    transition: all 0.3s ease;
}

.codex-contact-info-item:hover .codex-contact-icon {
    transform: scale(1.1) rotate(10deg);
}

.codex-contact-icon i {
    font-size: 24px;
    color: white;
}

.codex-contact-text h5 {
    font-size: 18px;
    color: #000080;
    margin: 0 0 8px;
    font-weight: 600;
}

.codex-contact-text p {
    font-size: 15px;
    color: #64748b;
    margin: 0;
    line-height: 1.6;
}

.codex-contact-form {
    flex: 1;
    background: white;
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 15px 40px rgba(0, 0, 128, 0.1);
    border: 1px solid rgba(0, 0, 128, 0.05);
    position: relative;
    overflow: hidden;
}

.codex-contact-form::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, #1e40af, #000080);
}

.codex-form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.codex-form-group {
    flex: 1;
    margin-bottom: 20px;
}

.codex-form-row .codex-form-group {
    margin-bottom: 0;
}

.codex-form-group label {
    display: block;
    font-size: 14px;
    color: #000080;
    margin-bottom: 8px;
    font-weight: 600;
}

.codex-form-control {
    width: 100%;
    padding: 15px;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    font-size: 15px;
    color: #1e293b;
    transition: all 0.3s ease;
    background: #f8fafc;
}

.codex-form-control:hover {
    border-color: rgba(0, 0, 128, 0.3);
    background: white;
}

.codex-form-control:focus {
    border-color: #000080;
    outline: none;
    box-shadow: 0 0 0 4px rgba(0, 0, 128, 0.1);
    background: white;
    transform: translateY(-2px);
}

.codex-form-submit {
    width: 100%;
    padding: 16px;
    background: linear-gradient(135deg, #000080, #1e40af);
    color: white;
    border: none;
    border-radius: 50px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 8px 20px rgba(0, 0, 128, 0.2);
    position: relative;
    overflow: hidden;
    margin-top: 10px;
}

.codex-form-submit::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.7s ease;
}

.codex-form-submit:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 30px rgba(0, 0, 128, 0.3);
    background: linear-gradient(135deg, #1e40af, #000080);
}

.codex-form-submit:hover::before {
    left: 100%;
}

/* Responsive styles */
@media (max-width: 992px) {
    .codex-contact-content {
        flex-direction: column;
    }
    
    .codex-contact-info, 
    .codex-contact-form {
        width: 100%;
    }
}

@media (max-width: 768px) {
    .codex-contact {
        padding: 70px 0;
    }
    
    .codex-section-title h2 {
        font-size: 28px;
    }
    
    .codex-contact-info, 
    .codex-contact-form {
        padding: 30px;
    }
    
    .codex-form-row {
        flex-direction: column;
        gap: 0;
    }
    
    .codex-contact-icon {
        width: 50px;
        height: 50px;
    }
    
    .codex-contact-icon i {
        font-size: 20px;
    }
}

@media (max-width: 480px) {
    .codex-contact {
        padding: 50px 0;
    }
    
    .codex-section-title h2 {
        font-size: 24px;
    }
    
    .codex-contact-info, 
    .codex-contact-form {
        padding: 25px;
    }
    
    .codex-contact-info-item {
        padding: 10px;
        gap: 15px;
    }
    
    .codex-contact-icon {
        width: 45px;
        height: 45px;
    }
    
    .codex-contact-icon i {
        font-size: 18px;
    }
    
    .codex-contact-text h5 {
        font-size: 16px;
    }
    
    .codex-contact-text p {
        font-size: 14px;
    }
    
    .codex-form-control {
        padding: 12px;
    }
    
    .codex-form-submit {
        padding: 14px;
    }
}

/* ===== Hero Section ===== */
.contact-hero {
    position: relative;
    min-height: 60vh;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    text-align: center;
    overflow: hidden;
    background: url('images/banners/contact-banner.jpg') center/cover no-repeat;
}

.contact-hero .hero-overlay {
    position: absolute;
    inset: 0;
    background: rgba(0, 0, 128, 0.6);
    backdrop-filter: blur(2px);
}

.contact-hero .hero-content {
    position: relative;
    z-index: 1;
    max-width: 800px;
    padding: 0 1.5rem;
}

.contact-hero h1 {
    font-size: clamp(2.5rem, 6vw, 4rem);
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1rem;
}

.contact-hero h1 span {
    color: #00b4d8;
}

.contact-hero p {
    font-size: clamp(1rem, 2vw, 1.25rem);
    margin-bottom: 2rem;
    opacity: 0.9;
}

.btn-primary {
    display: inline-block;
    padding: 0.9rem 2.5rem;
    border-radius: 50px;
    background: linear-gradient(135deg, #00b4d8 0%, #1e40af 100%);
    color: #ffffff;
    font-weight: 600;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 20px rgba(0, 0, 128, 0.3);
}

/* ===== Enhanced Contact Methods ===== */
.contact-methods {
    padding: 4rem 0;
}

.contact-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(230px, 1fr));
    gap: 2rem;
}

.contact-card {
    position: relative;
    text-align: center;
    padding: 2rem 1.5rem;
    border-radius: 1.25rem;
    background: #ffffff;
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.06);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.contact-card::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, rgba(0, 180, 216, 0.15), rgba(30, 64, 175, 0.15));
    opacity: 0.7;
    mix-blend-mode: multiply;
}

.contact-card i {
    font-size: 2.25rem;
    color: #1e40af;
    margin-bottom: 1rem;
    position: relative;
    z-index: 1;
}

.contact-card h3 {
    font-size: 1.125rem;
    color: #0f172a;
    margin-bottom: 0.25rem;
    font-weight: 600;
    position: relative;
    z-index: 1;
}

.contact-card p {
    font-size: 0.95rem;
    color: #64748b;
    position: relative;
    z-index: 1;
}

.contact-card:hover {
    transform: translateY(-6px);
    box-shadow: 0 18px 30px rgba(0, 35, 102, 0.15);
}

/* ===== Enhanced Contact Form ===== */
.contact-form {
    padding: 4rem 0 6rem;
}

.form-container {
    max-width: 650px;
    margin: 0 auto;
    background: #ffffff;
    border-radius: 1.5rem;
    padding: 2.5rem 2rem;
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.05);
}

.form-container h2 {
    text-align: center;
    font-size: 1.75rem;
    margin-bottom: 1.5rem;
    color: #1e40af;
    font-weight: 700;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    margin-bottom: 1.25rem;
}

.form-group label {
    font-size: 0.9rem;
    font-weight: 600;
    color: #0f172a;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 0.75rem 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 0.75rem;
    background: #f8fafc;
    transition: border-color 0.3s ease, background 0.3s ease, transform 0.2s ease;
    font-size: 0.95rem;
    color: #1e293b;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    border-color: #1e40af;
    background: #ffffff;
    outline: none;
    transform: translateY(-2px);
}

.submit-btn {
    background: linear-gradient(135deg, #00b4d8, #1e40af);
    color: #ffffff;
    border: none;
    border-radius: 9999px;
    padding: 0.9rem 2.5rem;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.submit-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 24px rgba(0, 35, 102, 0.25);
}

@media (max-width: 768px) {
    .contact-grid {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        gap: 1.25rem;
    }

    .form-container {
        padding: 2rem 1.5rem;
    }
}

/* ===== Split Form Flex Layout ===== */
.form-flex {
    display: flex;
    align-items: stretch;
    border-radius: 1.5rem;
    overflow: hidden;
    background: #ffffff;
    box-shadow: 0 12px 30px rgba(0,0,0,0.05);
}

.form-image {
    flex: 1;
    background-image: url('https://images.unsplash.com/photo-1529070538774-1843cb3265df?auto=format&fit=crop&w=900&q=60');
    background-size: cover;
    background-position: center;
    min-height: 450px;
}

.form-flex .form-container {
    flex: 1;
    max-width: none;
    margin: 0;
}

/* Responsive adjustments */
@media (max-width: 992px) {
    .form-image { display: none; }
    .form-flex .form-container { max-width: 100%; }
}