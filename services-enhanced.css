/* Services Enhanced Section CSS */
:root {
    --primary: #000080;
    --primary-light: #1e40af;
    --primary-dark: #000066;
    --secondary: #f59e0b;
    --accent: #10b981;
    --text-dark: #1e293b;
    --text-light: #64748b;
    --bg-light: #f8fafc;
    --bg-gradient: linear-gradient(135deg, #000080 0%, #1e40af 100%);
    --shadow-sm: 0 4px 6px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 10px 15px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 20px 25px rgba(0, 0, 0, 0.15);
    --shadow-xl: 0 25px 50px rgba(0, 0, 0, 0.25);
    --border-radius: 12px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Services Enhanced Section */
.services-enhanced {
    padding: 100px 0;
    background: var(--bg-light);
    position: relative;
    overflow: hidden;
}

.services-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="1" fill="%23000080" opacity="0.05"/></svg>') repeat;
    z-index: 0;
}

.services-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 1;
}

.services-header {
    text-align: center;
    margin-bottom: 60px;
}

.services-header .codex-section-title {
    margin-bottom: 20px;
}

.services-header .codex-section-title h6 {
    display: inline-block;
    padding: 8px 20px;
    background: rgba(0, 0, 128, 0.1);
    border-radius: 30px;
    font-size: 14px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 15px;
    color: var(--primary);
}

.services-header .codex-section-title h2 {
    font-size: 36px;
    font-weight: 800;
    color: var(--primary);
    line-height: 1.2;
    margin-bottom: 0;
}

.services-header p {
    font-size: 16px;
    line-height: 1.6;
    color: var(--text-light);
    max-width: 600px;
    margin: 0 auto;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
}

.service-card {
    background: #fff;
    border-radius: 20px;
    padding: 30px;
    box-shadow: var(--shadow-md);
    transition: var(--transition);
    border: 1px solid rgba(0, 0, 128, 0.1);
    position: relative;
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary), var(--primary-light));
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.4s ease;
}

.service-card:hover::before {
    transform: scaleX(1);
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
}

.service-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: 20px;
}

.service-icon {
    width: 70px;
    height: 70px;
    background:#A1AEBB;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    flex-shrink: 0;
}

.service-card:hover .service-icon {
    transform: rotateY(180deg);
}

.service-icon i {
    font-size: 30px;
    color: #fff;
    transition: var(--transition);
}

.service-card:hover .service-icon i {
    transform: rotateY(180deg);
}

.service-title {
    flex: 1;
    margin-left: 20px;
}

.service-title h3 {
    font-size: 22px;
    font-weight: 700;
    color: var(--primary);
    margin-bottom: 5px;
    line-height: 1.3;
}

.service-title p {
    font-size: 14px;
    color: var(--text-light);
    margin: 0;
}

.service-badge {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--secondary), #f97316);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.service-badge i {
    font-size: 18px;
    color: #fff;
}

.service-content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.service-description {
    font-size: 15px;
    line-height: 1.7;
    color: var(--text-light);
    margin-bottom: 25px;
}

.service-features {
    margin-bottom: 25px;
    flex-grow: 1;
}

.service-feature {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
}

.service-feature-icon {
    width: 20px;
    height: 20px;
    background: rgba(16, 185, 129, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.service-feature-icon i {
    font-size: 12px;
    color: var(--accent);
}

.service-feature-text {
    font-size: 14px;
    color: var(--text-dark);
    line-height: 1.5;
}

.service-action {
    margin-top: auto;
}

.service-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    color: var(--primary);
    font-weight: 700;
    text-decoration: none;
    font-size: 15px;
    transition: var(--transition);
    padding: 12px 20px;
    border-radius: 50px;
    background: rgba(0, 0, 128, 0.05);
    border: 1px solid rgba(0, 0, 128, 0.1);
}

.service-btn:hover {
    gap: 12px;
    background: var(--primary-light);
    color: #fff;
    transform: translateY(-2px);
    box-shadow: var(--shadow-sm);
}

.service-btn i {
    transition: var(--transition);
}

/* Responsive Design */
@media (max-width: 992px) {
    .services-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 25px;
    }
    
    .services-header .codex-section-title h2 {
        font-size: 32px;
    }
}

@media (max-width: 768px) {
    .services-enhanced {
        padding: 60px 0;
    }
    
    .services-container {
        padding: 0 15px;
    }
    
    .services-header {
        margin-bottom: 40px;
    }
    
    .services-header .codex-section-title h2 {
        font-size: 28px;
    }
    
    .services-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .service-card {
        padding: 25px;
    }
    
    .service-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
    
    .service-title {
        margin-left: 0;
    }
    
    .service-badge {
        align-self: flex-end;
    }
}

@media (max-width: 576px) {
    .services-header .codex-section-title h2 {
        font-size: 24px;
    }
    
    .service-card {
        padding: 20px;
    }
    
    .service-icon {
        width: 60px;
        height: 60px;
    }
    
    .service-icon i {
        font-size: 24px;
    }
    
    .service-title h3 {
        font-size: 20px;
    }
    
    .service-btn {
        width: 100%;
        justify-content: center;
    }
}
