/* PROFESSIONAL MOBILE NAVBAR - TOP SLIDE */

/* Mobile Menu Overlay */
.mobile-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: rgba(0, 0, 0, 0.6);
    z-index: 9998;
    opacity: 0;
    visibility: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(5px);
}

.mobile-menu-overlay.show {
    opacity: 1;
    visibility: visible;
}

/* Mobile Menu Container - Slides from Top */
.mobile-menu-sidebar {
    position: fixed;
    top: -100%;
    left: 0;
    width: 100%;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    z-index: 9999;
    transition: top 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    border-bottom-left-radius: 20px;
    border-bottom-right-radius: 20px;
    overflow: hidden;
    font-family: 'Inter', 'Gilroy', -apple-system, BlinkMacSystemFont, sans-serif;
}

.mobile-menu-sidebar.show {
    top: 0;
}

/* Professional Mobile Menu Header */
.mobile-menu-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 25px 30px;
    background: linear-gradient(135deg, #687FE5 0%, #5a6fd8 100%);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
}

.mobile-menu-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1.5" fill="rgba(255,255,255,0.1)"/></svg>');
    pointer-events: none;
}

.mobile-menu-logo {
    height: 60px;
    filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.2));
    position: relative;
    z-index: 1;
}

.mobile-menu-close {
    width: 45px;
    height: 45px;
    border: none;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 20px;
    color: white;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    position: relative;
    z-index: 1;
}

.mobile-menu-close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: rotate(90deg) scale(1.1);
}

/* Professional Mobile Menu Links */
.mobile-menu-links {
    list-style: none;
    padding: 0;
    margin: 0;
    background: white;
}

.mobile-menu-links li {
    margin: 0;
    border-bottom: 1px solid #f1f5f9;
}

.mobile-menu-links li:last-child {
    border-bottom: none;
}

.mobile-menu-links a {
    display: flex;
    align-items: center;
    padding: 18px 30px;
    color: #334155;
    text-decoration: none;
    font-size: 16px;
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
    background: white;
}

.mobile-menu-links a::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: #687FE5;
    transform: scaleY(0);
    transition: transform 0.3s ease;
}

.mobile-menu-links a:hover,
.mobile-menu-links a.active {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    color: #687FE5;
    padding-left: 35px;
}

.mobile-menu-links a:hover::before,
.mobile-menu-links a.active::before {
    transform: scaleY(1);
}

.mobile-menu-links a::after {
    content: '→';
    margin-left: auto;
    opacity: 0;
    transform: translateX(-10px);
    transition: all 0.3s ease;
    color: #687FE5;
    font-weight: bold;
}

.mobile-menu-links a:hover::after {
    opacity: 1;
    transform: translateX(0);
}

/* Hamburger Button */
.simple-hamburger {
    display: none;
    width: 40px;
    height: 40px;
    background: white;
    border: 1px solid #ddd;
    border-radius: 5px;
    cursor: pointer;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 4px;
}

.simple-hamburger span {
    width: 20px;
    height: 2px;
    background: #333;
    transition: all 0.3s ease;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .desktop-menu {
        display: none !important;
    }

    #links {
        display: none !important;
    }

    .apply-now-btn {
        display: none !important;
    }

    .simple-hamburger {
        display: flex !important;
    }
}
