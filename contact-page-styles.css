/*
 * CONTACT PAGE SPECIFIC STYLES
 * White Wings Visa - Contact Page Only CSS
 * Contains all styles specific to contact.html
 */

/* Import all required styles for contact page */
@import url('style.css');
@import url('professional-mobile-menu.css');
@import url('style-fix.css');
@import url('pages.css');
@import url('contact-enhanced.css');
@import url('universal-mobile-fix.css');
@import url('apply-now-button.css');
@import url('white-wings-colors.css');
@import url('text-visibility-fix.css');
@import url('white-wings-design-system.css');
@import url('button-icon-overrides.css');

/* Contact page specific overrides and enhancements */
.contact-hero-section {
    /* Contact page hero specific styles */
}

.contact-form-section {
    /* Contact page form section specific styles */
}

.contact-info-section {
    /* Contact page info section specific styles */
}

.contact-map-section {
    /* Contact page map section specific styles */
}

.contact-cta-section {
    /* Contact page CTA section specific styles */
}

/* Contact page specific responsive styles */
@media (max-width: 768px) {
    .contact-hero-section {
        /* Mobile specific contact hero styles */
    }
    
    .contact-form-section {
        /* Mobile specific contact form styles */
    }
}

/* Contact page specific animations */
@keyframes contact-bounce-in {
    from { opacity: 0; transform: scale(0.8); }
    to { opacity: 1; transform: scale(1); }
}

.contact-animate {
    animation: contact-bounce-in 0.7s ease-out;
}
