/* Enhanced Hero Section for About Page */

/* Hero Section */
.about-hero-section {
    background: linear-gradient(135deg, #3939fc 0%, #ffffff 100%);
    padding: 120px 0 100px;
    position: relative;
    overflow: hidden;
    color: white;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20"><circle cx="10" cy="10" r="1" fill="white" opacity="0.05"/></svg>') repeat;
    z-index: 2;
}

.hero-shapes {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 3;
}

.hero-shape {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    animation: float 15s infinite ease-in-out;
}

.hero-shape-1 {
    width: 400px;
    height: 400px;
    top: -150px;
    left: -150px;
    animation-delay: 0s;
}

.hero-shape-2 {
    width: 300px;
    height: 300px;
    bottom: -100px;
    right: 10%;
    animation-delay: 5s;
}

.hero-shape-3 {
    width: 200px;
    height: 200px;
    top: 30%;
    right: -80px;
    animation-delay: 2s;
}

@keyframes float {
    0%, 100% { transform: translateY(0) rotate(0deg); }
    25% { transform: translateY(-20px) rotate(5deg); }
    50% { transform: translateY(0) rotate(0deg); }
    75% { transform: translateY(20px) rotate(-5deg); }
}

.about-hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 5;
}

.about-hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.hero-text-content {
    text-align: left;
    animation: fadeInLeft 1s ease-out;
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    background-color:#D9EAFD;
    padding: 10px 20px;
    border-radius: 50px;
    margin-bottom: 25px;
    backdrop-filter: blur(5px);
    animation: fadeInDown 1s ease-out;
    
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.hero-badge i {
    font-size: 1.3rem;
    color: #222;
}

.hero-badge span {
    font-weight: 700;
    letter-spacing: 1px;
    color: #222;
}

.about-hero-content h1 {
    font-size: 3.5rem;
    font-weight: 600;
    margin-bottom: 25px;
    line-height: 1.2;
    -webkit-background-clip: text;
    background-clip: text;
    animation: fadeInUp 1s ease-out 0.2s both;
    color: black;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.hero-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 35px;
    line-height: 1.8;
    animation: fadeInUp 1s ease-out 0.4s both;
    max-width: 90%;
}

.hero-actions {
    display: flex;
    gap: 20px;
    margin-top: 40px;
    animation: fadeInUp 1s ease-out 0.6s both;
}

.btn-primary, .btn-secondary {
    padding: 16px 30px;
    border-radius: 50px;
    font-weight: 700;
    font-size: 1.1rem;
    display: inline-flex;
    align-items: center;
    gap: 10px;
    transition: all 0.3s ease;
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.btn-primary::before, .btn-secondary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.7s ease;
}

.btn-primary:hover::before, .btn-secondary:hover::before {
    left: 100%;
}

.btn-primary {
    background: white;
    color: #000080;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.btn-primary:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
    background: #f8fafc;
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-5px);
    border-color: rgba(255, 255, 255, 0.5);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

.btn-primary i, .btn-secondary i {
    font-size: 1.2rem;
    transition: transform 0.3s ease;
}

.btn-primary:hover i, .btn-secondary:hover i {
    transform: translateX(5px);
}

.hero-visual {
    position: relative;
    animation: fadeInRight 1s ease-out;
    perspective: 1000px;
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.hero-image-container {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
    transform: rotateY(-5deg);
    transition: all 0.5s ease;
    border: 5px solid rgba(255, 255, 255, 0.1);
}

.hero-image-container:hover {
    transform: rotateY(0deg) translateY(-10px);
    box-shadow: 0 35px 60px rgba(0, 0, 0, 0.4);
}

.hero-image {
    width: 100%;
    height: auto;
    display: block;
    transition: all 0.5s ease;
}

.hero-image-container:hover .hero-image {
    transform: scale(1.05);
}

.image-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 30px;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
    transition: all 0.3s ease;
}

.hero-image-container:hover .image-overlay {
    padding-bottom: 40px;
}

.overlay-badge {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    background: rgba(255, 255, 255, 0.2);
    padding: 10px 20px;
    border-radius: 50px;
    color: white;
    font-weight: 700;
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.hero-image-container:hover .overlay-badge {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.3);
}

.overlay-badge i {
    font-size: 1.3rem;
}

/* Mobile Responsive */
@media (max-width: 992px) {
    .about-hero-content h1 {
        font-size: 3rem;
    }
    
    .hero-subtitle {
        font-size: 1.1rem;
    }
}

@media (max-width: 768px) {
    .about-hero-section {
        padding: 100px 0 80px;
    }
    
    .about-hero-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }
    
    .hero-text-content {
        text-align: center;
    }
    
    .about-hero-content h1 {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.1rem;
        margin: 0 auto 30px;
    }
    
    .hero-actions {
        justify-content: center;
    }
    
    .hero-image-container {
        transform: rotateY(0);
        max-width: 500px;
        margin: 0 auto;
    }
}

@media (max-width: 480px) {
    .about-hero-section {
        padding: 80px 0 60px;
    }
    
    .about-hero-content h1 {
        font-size: 2rem;
    }
    
    .hero-subtitle {
        font-size: 1rem;
    }
    
    .hero-badge {
        padding: 8px 16px;
    }
    
    .btn-primary, .btn-secondary {
        padding: 14px 24px;
        font-size: 1rem;
    }
    
    .hero-actions {
        flex-direction: column;
        gap: 15px;
    }
}