/*
 * HOME PAGE HERO SECTION MOBILE FIX
 * Fix text cut off issue on mobile devices
 */

/* Mobile Hero Section Fix */
@media screen and (max-width: 768px) {
    .hero {
        min-height: 100vh !important;
        padding-bottom: 3rem !important;
        overflow: visible !important;
    }
    
    .hero-container {
        width: 95% !important;
        flex-direction: column !important;
        height: auto !important;
        transform: none !important;
        left: auto !important;
        top: auto !important;
        position: static !important;
        margin: 8vh auto 2rem auto !important;
        padding: 1rem 0.5rem 2rem 0.5rem !important;
        gap: 1.5rem !important;
        min-height: auto !important;
    }
    
    .hero-text {
        width: 100% !important;
        padding: 1rem 0.5rem 1.5rem 0.5rem !important;
        text-align: center !important;
        order: 2 !important;
        min-height: auto !important;
    }
    
    .hero-text h1 {
        font-size: 2.8rem !important;
        width: 100% !important;
        line-height: 1.3 !important;
        text-align: center !important;
        margin-bottom: 1.2rem !important;
        white-space: normal !important;
        word-wrap: break-word !important;
        overflow-wrap: break-word !important;
    }
    
    .hero-text p {
        width: 100% !important;
        font-size: 1.2rem !important;
        line-height: 1.6 !important;
        text-align: center !important;
        margin-bottom: 1.8rem !important;
        white-space: normal !important;
        word-wrap: break-word !important;
        overflow-wrap: break-word !important;
        padding: 0 0.5rem !important;
    }
    
    .hero-text button {
        width: 95% !important;
        max-width: 350px !important;
        font-size: 1.1rem !important;
        padding: 1rem 2rem !important;
        margin: 0 auto 2rem auto !important;
        background: #A6B3C0 !important;
        color: #222 !important;
        border: none !important;
        border-radius: 50px !important;
    }
    
    .hero-text button:hover {
        background: #9AA6B2 !important;
        color: #222 !important;
    }
    
    .hero-container > .slider {
        width: 100% !important;
        height: 45vh !important;
        margin: 0 auto 1rem auto !important;
        order: 1 !important;
    }
}

/* Small Mobile Devices */
@media screen and (max-width: 480px) {
    .hero {
        min-height: 100vh !important;
        padding-bottom: 4rem !important;
    }
    
    .hero-container {
        margin: 6vh auto 3rem auto !important;
        padding: 0.5rem 0.3rem 3rem 0.3rem !important;
        gap: 1rem !important;
    }
    
    .hero-text {
        padding: 0.8rem 0.3rem 2rem 0.3rem !important;
    }
    
    .hero-text h1 {
        font-size: 2.2rem !important;
        margin-bottom: 1rem !important;
        line-height: 1.2 !important;
    }
    
    .hero-text p {
        font-size: 1.1rem !important;
        margin-bottom: 1.5rem !important;
        line-height: 1.5 !important;
        padding: 0 0.3rem !important;
    }
    
    .hero-text button {
        font-size: 1rem !important;
        padding: 0.9rem 1.5rem !important;
        width: 98% !important;
        max-width: 320px !important;
        margin: 0 auto 2rem auto !important;
    }
    
    .hero-container > .slider {
        height: 35vh !important;
        margin: 0 auto 1.5rem auto !important;
    }
}

/* Extra Small Mobile Devices */
@media screen and (max-width: 360px) {
    .hero {
        padding-bottom: 5rem !important;
    }
    
    .hero-container {
        margin: 5vh auto 4rem auto !important;
        padding: 0.3rem 0.2rem 4rem 0.2rem !important;
        width: 98% !important;
    }
    
    .hero-text {
        padding: 0.5rem 0.2rem 3rem 0.2rem !important;
    }
    
    .hero-text h1 {
        font-size: 1.8rem !important;
        margin-bottom: 0.8rem !important;
        line-height: 1.1 !important;
    }
    
    .hero-text p {
        font-size: 1rem !important;
        margin-bottom: 1.2rem !important;
        line-height: 1.4 !important;
        padding: 0 0.2rem !important;
    }
    
    .hero-text button {
        font-size: 0.9rem !important;
        padding: 0.8rem 1.2rem !important;
        width: 100% !important;
        max-width: 280px !important;
        margin: 0 auto 3rem auto !important;
    }
    
    .hero-container > .slider {
        height: 30vh !important;
        margin: 0 auto 2rem auto !important;
    }
}

/* Landscape Mobile Fix */
@media screen and (max-width: 768px) and (orientation: landscape) {
    .hero {
        min-height: 100vh !important;
        padding-bottom: 2rem !important;
    }
    
    .hero-container {
        margin: 5vh auto 1rem auto !important;
        padding: 0.5rem 1rem 2rem 1rem !important;
    }
    
    .hero-text {
        padding: 0.5rem 1rem 1rem 1rem !important;
    }
    
    .hero-text h1 {
        font-size: 2.5rem !important;
        margin-bottom: 0.8rem !important;
    }
    
    .hero-text p {
        font-size: 1.1rem !important;
        margin-bottom: 1rem !important;
        padding: 0 1rem !important;
    }
    
    .hero-text button {
        margin: 0 auto 1.5rem auto !important;
    }
    
    .hero-container > .slider {
        height: 40vh !important;
        margin: 0 auto 1rem auto !important;
    }
}

/* Ensure text is never cut off */
.hero-text h1,
.hero-text p {
    overflow: visible !important;
    text-overflow: clip !important;
    white-space: normal !important;
    word-break: break-word !important;
    hyphens: auto !important;
}

/* Ensure container has enough space */
.hero-container {
    overflow: visible !important;
}

.hero-text {
    overflow: visible !important;
}
