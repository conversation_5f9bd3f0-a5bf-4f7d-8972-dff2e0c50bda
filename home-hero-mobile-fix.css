/*
 * HOME PAGE HERO SECTION MOBILE FIX
 * Fix text cut off issue on mobile devices
 */

/* Mobile Hero Section Fix */
@media screen and (max-width: 768px) {
    .hero {
        min-height: 100vh !important;
        padding-bottom: 5rem !important;
        overflow: visible !important;
        height: auto !important;
    }

    .hero-container {
        width: 95% !important;
        flex-direction: column !important;
        height: auto !important;
        transform: none !important;
        left: auto !important;
        top: auto !important;
        position: static !important;
        margin: 5vh auto 3rem auto !important;
        padding: 1rem 0.5rem 4rem 0.5rem !important;
        gap: 1.5rem !important;
        min-height: auto !important;
        overflow: visible !important;
    }
    
    .hero-text {
        width: 100% !important;
        padding: 1rem 1rem 3rem 1rem !important;
        text-align: center !important;
        order: 2 !important;
        min-height: auto !important;
        overflow: visible !important;
        box-sizing: border-box !important;
    }

    .hero-text h1 {
        font-size: 2.5rem !important;
        width: 100% !important;
        line-height: 1.2 !important;
        text-align: center !important;
        margin-bottom: 1rem !important;
        white-space: normal !important;
        word-wrap: break-word !important;
        overflow-wrap: break-word !important;
        overflow: visible !important;
        padding: 0 0.5rem !important;
    }

    .hero-text p {
        width: 100% !important;
        font-size: 1.1rem !important;
        line-height: 1.5 !important;
        text-align: center !important;
        margin-bottom: 2rem !important;
        white-space: normal !important;
        word-wrap: break-word !important;
        overflow-wrap: break-word !important;
        padding: 0 1rem !important;
        overflow: visible !important;
        box-sizing: border-box !important;
        max-width: 100% !important;
    }
    
    .hero-text button {
        width: 95% !important;
        max-width: 350px !important;
        font-size: 1.1rem !important;
        padding: 1rem 2rem !important;
        margin: 0 auto 2rem auto !important;
        background: #A6B3C0 !important;
        color: #222 !important;
        border: none !important;
        border-radius: 50px !important;
    }
    
    .hero-text button:hover {
        background: #9AA6B2 !important;
        color: #222 !important;
    }
    
    .hero-container > .slider {
        width: 100% !important;
        height: 45vh !important;
        margin: 0 auto 1rem auto !important;
        order: 1 !important;
    }
}

/* Small Mobile Devices */
@media screen and (max-width: 480px) {
    .hero {
        min-height: 100vh !important;
        padding-bottom: 6rem !important;
        height: auto !important;
    }

    .hero-container {
        margin: 3vh auto 4rem auto !important;
        padding: 0.5rem 0.5rem 5rem 0.5rem !important;
        gap: 1rem !important;
        width: 98% !important;
    }

    .hero-text {
        padding: 1rem 0.5rem 4rem 0.5rem !important;
        overflow: visible !important;
    }

    .hero-text h1 {
        font-size: 2rem !important;
        margin-bottom: 0.8rem !important;
        line-height: 1.1 !important;
        padding: 0 0.3rem !important;
        overflow: visible !important;
    }

    .hero-text p {
        font-size: 1rem !important;
        margin-bottom: 2rem !important;
        line-height: 1.4 !important;
        padding: 0 0.5rem !important;
        overflow: visible !important;
        max-width: 100% !important;
    }
    
    .hero-text button {
        font-size: 1rem !important;
        padding: 0.9rem 1.5rem !important;
        width: 98% !important;
        max-width: 320px !important;
        margin: 0 auto 2rem auto !important;
    }
    
    .hero-container > .slider {
        height: 35vh !important;
        margin: 0 auto 1.5rem auto !important;
    }
}

/* Extra Small Mobile Devices */
@media screen and (max-width: 360px) {
    .hero {
        padding-bottom: 7rem !important;
        min-height: 100vh !important;
    }

    .hero-container {
        margin: 2vh auto 5rem auto !important;
        padding: 0.5rem 0.3rem 6rem 0.3rem !important;
        width: 98% !important;
        overflow: visible !important;
    }

    .hero-text {
        padding: 1rem 0.3rem 5rem 0.3rem !important;
        overflow: visible !important;
    }

    .hero-text h1 {
        font-size: 1.6rem !important;
        margin-bottom: 0.6rem !important;
        line-height: 1.1 !important;
        padding: 0 0.2rem !important;
        overflow: visible !important;
    }

    .hero-text p {
        font-size: 0.9rem !important;
        margin-bottom: 2rem !important;
        line-height: 1.3 !important;
        padding: 0 0.3rem !important;
        overflow: visible !important;
        max-width: 100% !important;
    }
    
    .hero-text button {
        font-size: 0.9rem !important;
        padding: 0.8rem 1.2rem !important;
        width: 100% !important;
        max-width: 280px !important;
        margin: 0 auto 3rem auto !important;
    }
    
    .hero-container > .slider {
        height: 30vh !important;
        margin: 0 auto 2rem auto !important;
    }
}

/* Landscape Mobile Fix */
@media screen and (max-width: 768px) and (orientation: landscape) {
    .hero {
        min-height: 100vh !important;
        padding-bottom: 2rem !important;
    }
    
    .hero-container {
        margin: 5vh auto 1rem auto !important;
        padding: 0.5rem 1rem 2rem 1rem !important;
    }
    
    .hero-text {
        padding: 0.5rem 1rem 1rem 1rem !important;
    }
    
    .hero-text h1 {
        font-size: 2.5rem !important;
        margin-bottom: 0.8rem !important;
    }
    
    .hero-text p {
        font-size: 1.1rem !important;
        margin-bottom: 1rem !important;
        padding: 0 1rem !important;
    }
    
    .hero-text button {
        margin: 0 auto 1.5rem auto !important;
    }
    
    .hero-container > .slider {
        height: 40vh !important;
        margin: 0 auto 1rem auto !important;
    }
}

/* Ensure text is never cut off - Global Fix */
.hero-text h1,
.hero-text p {
    overflow: visible !important;
    text-overflow: clip !important;
    white-space: normal !important;
    word-break: break-word !important;
    hyphens: auto !important;
    max-height: none !important;
    height: auto !important;
}

/* Ensure container has enough space */
.hero-container {
    overflow: visible !important;
    max-height: none !important;
    height: auto !important;
}

.hero-text {
    overflow: visible !important;
    max-height: none !important;
    height: auto !important;
}

/* Force mobile viewport to show full content */
@media screen and (max-width: 768px) {
    body {
        overflow-x: hidden !important;
        overflow-y: auto !important;
    }

    .hero {
        overflow: visible !important;
        max-height: none !important;
        height: auto !important;
    }

    /* Prevent any parent containers from cutting text */
    .hero *,
    .hero-container *,
    .hero-text * {
        overflow: visible !important;
        max-height: none !important;
        height: auto !important;
    }
}

/* Additional safety for very long text */
@media screen and (max-width: 480px) {
    .hero-text p {
        word-spacing: normal !important;
        letter-spacing: normal !important;
        text-indent: 0 !important;
        text-align: center !important;
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
    }
}
