/* Professional Study Hero Section Styles */
.study-hero-section {
    position: relative;
    padding: 120px 0 80px;
    overflow: hidden;
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
    color: white;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 128, 0.1);
    z-index: 1;
}

.hero-shapes {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.hero-shape {
    position: absolute;
    border-radius: 50%;
    opacity: 0.1;
}

.hero-shape-1 {
    top: -100px;
    right: -100px;
    width: 300px;
    height: 300px;
    background: white;
}

.hero-shape-2 {
    bottom: -50px;
    left: -50px;
    width: 200px;
    height: 200px;
    background: white;
}

.hero-shape-3 {
    top: 40%;
    right: 20%;
    width: 100px;
    height: 100px;
    background: white;
}

.study-hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 2;
}

.study-hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.hero-text-content {
    text-align: left;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background:#A1AEBB;
    padding: 8px 16px;
    border-radius: 50px;
    margin-bottom: 20px;
}

.hero-badge i {
    font-size: 1.2rem;
}

.hero-text-content h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 20px;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 30px;
}

.hero-actions {
    display: flex;
    gap: 20px;
    margin-top: 30px;
}

.btn-primary, .btn-secondary {
    padding: 14px 24px;
    border-radius: 50px;
    font-weight: 600;
    font-size: 1rem;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    text-decoration: none;
}

.btn-primary {
    background: white;
    color: #1e3a8a;
    box-shadow: 0 10px 25px rgba(255, 255, 255, 0.2);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 30px rgba(255, 255, 255, 0.3);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-3px);
}

.hero-visual {
    position: relative;
}

.hero-image-container {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
}

.hero-image {
    width: 100%;
    height: auto;
    display: block;
}

.image-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 30px;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
}

.overlay-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.2);
    padding: 8px 16px;
    border-radius: 50px;
    color: white;
    font-weight: 600;
}

.overlay-badge i {
    font-size: 1.2rem;
}

/* Quick Assessment Form */
.quick-assessment-section {
    background: white;
    padding: 40px 0;
    margin-top: 40px;
    position: relative;
    z-index: 2;
    border-radius: 20px 20px 0 0;
    margin-top: -20px;
}

.assessment-container {
    max-width: 900px;
    margin: 0 auto;
    padding: 30px;
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 128, 0.1);
}

/* Responsive styles */
@media (max-width: 992px) {
    .study-hero-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }
    
    .hero-text-content {
        text-align: center;
    }
    
    .hero-actions {
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .study-hero-section {
        padding: 100px 0 60px;
    }
    
    .hero-text-content h1 {
        font-size: 2.2rem;
    }
    
    .hero-actions {
        flex-direction: column;
        align-items: center;
    }
}